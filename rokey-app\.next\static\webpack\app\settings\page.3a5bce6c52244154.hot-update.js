"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/settings/page",{

/***/ "(app-pages-browser)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SettingsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction SettingsPage() {\n    var _user_user_metadata, _user_user_metadata1, _user_user_metadata2;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_8__.useSubscription)();\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_7__.createSupabaseBrowserClient)();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('account');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCurrentPassword, setShowCurrentPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNewPassword, setShowNewPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDeleteModal, setShowDeleteModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showResetModal, setShowResetModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isResetLoading, setIsResetLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [passwordData, setPasswordData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n    });\n    const [notificationSettings, setNotificationSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        emailNotifications: true,\n        securityAlerts: true,\n        usageAlerts: true,\n        marketingEmails: false\n    });\n    const [userStats, setUserStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        configCount: 0,\n        apiKeyCount: 0,\n        userApiKeyCount: 0\n    });\n    const sidebarItems = [\n        {\n            id: 'account',\n            label: 'Account settings',\n            icon: _barrel_optimize_names_BellIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            id: 'notifications',\n            label: 'Notifications',\n            icon: _barrel_optimize_names_BellIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            id: 'security',\n            label: 'Security',\n            icon: _barrel_optimize_names_BellIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        },\n        {\n            id: 'danger',\n            label: 'Danger zone',\n            icon: _barrel_optimize_names_BellIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        }\n    ];\n    // Load user data and stats\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsPage.useEffect\": ()=>{\n            if (user) {\n                loadUserStats();\n                loadNotificationSettings();\n            }\n        }\n    }[\"SettingsPage.useEffect\"], [\n        user\n    ]);\n    const loadUserStats = async ()=>{\n        try {\n            // Get configuration count\n            const configResponse = await fetch('/api/custom-configs');\n            const configData = configResponse.ok ? await configResponse.json() : [];\n            const configs = Array.isArray(configData) ? configData : [];\n            // Get user API keys count\n            const userKeysResponse = await fetch('/api/user-api-keys');\n            const userKeysData = userKeysResponse.ok ? await userKeysResponse.json() : {\n                api_keys: []\n            };\n            const userKeys = userKeysData.api_keys || [];\n            // Get total API keys count across all configs\n            let totalApiKeys = 0;\n            for (const config of configs){\n                try {\n                    const keysResponse = await fetch(\"/api/custom-configs/\".concat(config.id, \"/keys\"));\n                    const keysData = keysResponse.ok ? await keysResponse.json() : {\n                        api_keys: []\n                    };\n                    totalApiKeys += (keysData.api_keys || []).length;\n                } catch (error) {\n                    console.error('Error loading keys for config:', config.id, error);\n                }\n            }\n            setUserStats({\n                configCount: configs.length,\n                apiKeyCount: totalApiKeys,\n                userApiKeyCount: userKeys.length\n            });\n        } catch (error) {\n            console.error('Error loading user stats:', error);\n        }\n    };\n    const loadNotificationSettings = async ()=>{\n        try {\n            // Load from user metadata or local storage\n            const savedSettings = localStorage.getItem('notification_settings');\n            if (savedSettings) {\n                setNotificationSettings(JSON.parse(savedSettings));\n            }\n        } catch (error) {\n            console.error('Error loading notification settings:', error);\n        }\n    };\n    const handlePasswordChange = async (e)=>{\n        e.preventDefault();\n        if (!passwordData.currentPassword.trim()) {\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('Current password is required');\n            return;\n        }\n        if (!passwordData.newPassword.trim()) {\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('New password is required');\n            return;\n        }\n        if (passwordData.newPassword !== passwordData.confirmPassword) {\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('New passwords do not match');\n            return;\n        }\n        if (passwordData.newPassword.length < 8) {\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('Password must be at least 8 characters long');\n            return;\n        }\n        if (passwordData.newPassword === passwordData.currentPassword) {\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('New password must be different from current password');\n            return;\n        }\n        setLoading(true);\n        try {\n            // First verify current password by attempting to sign in\n            const { error: signInError } = await supabase.auth.signInWithPassword({\n                email: (user === null || user === void 0 ? void 0 : user.email) || '',\n                password: passwordData.currentPassword\n            });\n            if (signInError) {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('Current password is incorrect');\n                setLoading(false);\n                return;\n            }\n            // If current password is correct, update to new password\n            const { error: updateError } = await supabase.auth.updateUser({\n                password: passwordData.newPassword\n            });\n            if (updateError) throw updateError;\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('Password updated successfully');\n            setPasswordData({\n                currentPassword: '',\n                newPassword: '',\n                confirmPassword: ''\n            });\n        } catch (error) {\n            console.error('Password update error:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(error.message || 'Failed to update password');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleNotificationUpdate = async ()=>{\n        setLoading(true);\n        try {\n            // Save to local storage and user metadata\n            localStorage.setItem('notification_settings', JSON.stringify(notificationSettings));\n            // Also save to user metadata for persistence across devices\n            const { error } = await supabase.auth.updateUser({\n                data: {\n                    notification_preferences: notificationSettings\n                }\n            });\n            if (error) throw error;\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('Notification preferences saved successfully');\n        } catch (error) {\n            console.error('Notification update error:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('Failed to save notification preferences');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handlePasswordReset = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) {\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('No email address found');\n            return;\n        }\n        setIsResetLoading(true);\n        try {\n            const { error } = await supabase.auth.resetPasswordForEmail(user.email, {\n                redirectTo: \"\".concat(window.location.origin, \"/auth/reset-password\")\n            });\n            if (error) throw error;\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('Password reset email sent!', 'Check your inbox for instructions to reset your password.');\n            setShowResetModal(false);\n        } catch (error) {\n            console.error('Password reset error:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('Failed to send reset email', error.message || 'Please try again.');\n        } finally{\n            setIsResetLoading(false);\n        }\n    };\n    const handleAccountDeletion = async ()=>{\n        if (!user) return;\n        setLoading(true);\n        try {\n            // Call our account deletion API\n            const response = await fetch('/api/user/delete-account', {\n                method: 'DELETE',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || 'Failed to delete account');\n            }\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('Account deleted successfully. You will be redirected to the homepage.');\n            // Sign out and redirect\n            await supabase.auth.signOut();\n            setTimeout(()=>{\n                window.location.href = '/';\n            }, 2000);\n        } catch (error) {\n            console.error('Account deletion error:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(error.message || 'Failed to delete account. Please contact support.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Settings\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-1\",\n                            children: \"Manage your account settings and preferences\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 265,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"-mb-px flex space-x-8\",\n                    children: sidebarItems.map((item)=>{\n                        const Icon = item.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveSection(item.id),\n                            className: \"flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors \".concat(activeSection === item.id ? 'border-orange-500 text-orange-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 17\n                                }, this),\n                                item.label\n                            ]\n                        }, item.id, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl\",\n                children: [\n                    activeSection === 'account' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Email address\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: [\n                                                            \"Your email address is \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: user === null || user === void 0 ? void 0 : user.email\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 74\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 mt-1\",\n                                                        children: \"This is used for login and important notifications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-md transition-colors\",\n                                                onClick: ()=>sonner__WEBPACK_IMPORTED_MODULE_6__.toast.info('Email changes are not currently supported. Please contact support if needed.'),\n                                                children: \"Change\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-6\",\n                                        children: \"Account Information\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-900 mb-1\",\n                                                                children: \"Full Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || (user === null || user === void 0 ? void 0 : (_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : _user_user_metadata1.first_name) + ' ' + (user === null || user === void 0 ? void 0 : (_user_user_metadata2 = user.user_metadata) === null || _user_user_metadata2 === void 0 ? void 0 : _user_user_metadata2.last_name) || 'Not set'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-900 mb-1\",\n                                                                children: \"Account Created\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: (user === null || user === void 0 ? void 0 : user.created_at) ? new Date(user.created_at).toLocaleDateString('en-US', {\n                                                                    year: 'numeric',\n                                                                    month: 'long',\n                                                                    day: 'numeric'\n                                                                }) : 'Unknown'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-gray-900 mb-1\",\n                                                            children: \"Account Usage\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1 text-sm text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        userStats.configCount,\n                                                                        \" configurations\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        userStats.apiKeyCount,\n                                                                        \" API keys\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        userStats.userApiKeyCount,\n                                                                        \" user-generated keys\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-6\",\n                                        children: \"Password\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handlePasswordChange,\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"currentPassword\",\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Current password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative mt-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                id: \"currentPassword\",\n                                                                type: showCurrentPassword ? \"text\" : \"password\",\n                                                                value: passwordData.currentPassword,\n                                                                onChange: (e)=>setPasswordData((prev)=>({\n                                                                            ...prev,\n                                                                            currentPassword: e.target.value\n                                                                        })),\n                                                                placeholder: \"••••••••••\",\n                                                                className: \"pr-10\",\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>setShowCurrentPassword(!showCurrentPassword),\n                                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                                                children: showCurrentPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 50\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 89\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                htmlFor: \"newPassword\",\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"New password\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                        id: \"newPassword\",\n                                                                        type: showNewPassword ? \"text\" : \"password\",\n                                                                        value: passwordData.newPassword,\n                                                                        onChange: (e)=>setPasswordData((prev)=>({\n                                                                                    ...prev,\n                                                                                    newPassword: e.target.value\n                                                                                })),\n                                                                        placeholder: \"••••••••••\",\n                                                                        className: \"pr-10\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 380,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>setShowNewPassword(!showNewPassword),\n                                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                                                        children: showNewPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 394,\n                                                                            columnNumber: 48\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 394,\n                                                                            columnNumber: 87\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 389,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                htmlFor: \"confirmPassword\",\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Confirm new password\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 399,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                        id: \"confirmPassword\",\n                                                                        type: showConfirmPassword ? \"text\" : \"password\",\n                                                                        value: passwordData.confirmPassword,\n                                                                        onChange: (e)=>setPasswordData((prev)=>({\n                                                                                    ...prev,\n                                                                                    confirmPassword: e.target.value\n                                                                                })),\n                                                                        placeholder: \"••••••••••\",\n                                                                        className: \"pr-10\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 401,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>setShowConfirmPassword(!showConfirmPassword),\n                                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                                                        children: showConfirmPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 415,\n                                                                            columnNumber: 52\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 415,\n                                                                            columnNumber: 91\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 410,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 398,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            \"Can't remember your current password? \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                                                onClick: ()=>setShowResetModal(true),\n                                                                children: \"Reset your password\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 63\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"submit\",\n                                                        disabled: loading,\n                                                        className: \"bg-orange-600 hover:bg-orange-700\",\n                                                        children: loading ? 'Saving...' : 'Save password'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 15\n                    }, this),\n                    activeSection === 'notifications' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-6\",\n                                    children: \"Notification Preferences\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        Object.entries(notificationSettings).map((param)=>{\n                                            let [key, value] = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: [\n                                                                    key === 'emailNotifications' && 'Email Notifications',\n                                                                    key === 'securityAlerts' && 'Security Alerts',\n                                                                    key === 'usageAlerts' && 'Usage Alerts',\n                                                                    key === 'marketingEmails' && 'Marketing Emails'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500 mt-1\",\n                                                                children: [\n                                                                    key === 'emailNotifications' && 'Receive general email notifications',\n                                                                    key === 'securityAlerts' && 'Get notified about security events',\n                                                                    key === 'usageAlerts' && 'Alerts about API usage and limits',\n                                                                    key === 'marketingEmails' && 'Product updates and promotional content'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 453,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: value,\n                                                                onChange: (e)=>setNotificationSettings((prev)=>({\n                                                                            ...prev,\n                                                                            [key]: e.target.checked\n                                                                        })),\n                                                                className: \"sr-only peer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, key, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 23\n                                            }, this);\n                                        }),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pt-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: handleNotificationUpdate,\n                                                disabled: loading,\n                                                className: \"bg-orange-600 hover:bg-orange-700\",\n                                                children: loading ? 'Saving...' : 'Save preferences'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 441,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 15\n                    }, this),\n                    activeSection === 'security' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"Security Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Secure\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-900 mb-2\",\n                                                        children: \"Password Security\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-3\",\n                                                        children: \"Change your password regularly for better security\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setActiveSection('account'),\n                                                        className: \"text-orange-600 border-orange-300 hover:bg-orange-50\",\n                                                        children: \"Change Password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-gray-900 mb-1\",\n                                                            children: \"Account Activity\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                userStats.configCount + userStats.apiKeyCount + userStats.userApiKeyCount,\n                                                                \" total resources created\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-gray-900 mb-1\",\n                                                            children: \"Account Created\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: (user === null || user === void 0 ? void 0 : user.created_at) ? new Date(user.created_at).toLocaleDateString('en-US', {\n                                                                year: 'numeric',\n                                                                month: 'long',\n                                                                day: 'numeric'\n                                                            }) : 'Unknown'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 516,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 483,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 482,\n                        columnNumber: 15\n                    }, this),\n                    activeSection === 'danger' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg border border-red-200 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-6 w-6 text-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-red-900\",\n                                            children: \"Danger Zone\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 rounded-lg p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-medium text-red-900 mb-3\",\n                                            children: \"Delete Account\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-700 mb-4\",\n                                            children: \"Permanently delete your RouKey account and all associated data. This action cannot be undone.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white border border-red-200 rounded-md p-4 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"text-sm font-medium text-red-900 mb-2\",\n                                                    children: \"This will permanently delete:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-sm text-red-700 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                \"• \",\n                                                                userStats.configCount,\n                                                                \" API configurations\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                \"• \",\n                                                                userStats.apiKeyCount,\n                                                                \" API keys\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                \"• \",\n                                                                userStats.userApiKeyCount,\n                                                                \" user-generated API keys\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• All usage logs and analytics\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Your subscription and billing information\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            className: \"text-red-600 border-red-300 hover:bg-red-100 hover:border-red-400\",\n                                            onClick: ()=>setShowDeleteModal(true),\n                                            disabled: loading,\n                                            children: \"Delete my account\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 553,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 532,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 531,\n                        columnNumber: 15\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 296,\n                columnNumber: 7\n            }, this),\n            showResetModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-2xl max-w-md w-full p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EnvelopeIcon, {\n                                        className: \"h-5 w-5 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 573,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Reset Your Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 575,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 571,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: [\n                                \"We'll send a password reset link to \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: user === null || user === void 0 ? void 0 : user.email\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 579,\n                                    columnNumber: 51\n                                }, this),\n                                \". You can use this link to create a new password.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 578,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-blue-900 mb-2\",\n                                    children: \"What happens next:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 584,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-blue-700 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• You'll receive an email with a secure reset link\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Click the link to create a new password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 587,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Your current password will remain active until you complete the reset\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 588,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 585,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 583,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowResetModal(false),\n                                    className: \"flex-1\",\n                                    disabled: isResetLoading,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handlePasswordReset,\n                                    disabled: isResetLoading,\n                                    className: \"flex-1 bg-blue-600 hover:bg-blue-700 text-white\",\n                                    children: isResetLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Sending...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 19\n                                    }, this) : 'Send Reset Link'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 592,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 570,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 569,\n                columnNumber: 9\n            }, this),\n            showDeleteModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-2xl max-w-md w-full p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-6 w-6 text-red-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 625,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Delete Account\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 626,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 624,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Are you absolutely sure you want to delete your account? This action cannot be undone.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 629,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-red-900 mb-2\",\n                                    children: \"This will permanently delete:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 634,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-red-700 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                userStats.configCount,\n                                                \" API configurations\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 636,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                userStats.apiKeyCount,\n                                                \" API keys\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 637,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                userStats.userApiKeyCount,\n                                                \" user-generated API keys\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 638,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• All usage logs and analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 639,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Your subscription and billing information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 635,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 633,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowDeleteModal(false),\n                                    className: \"flex-1\",\n                                    disabled: loading,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 645,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleAccountDeletion,\n                                    disabled: loading,\n                                    className: \"flex-1 bg-red-600 hover:bg-red-700 text-white\",\n                                    children: loading ? 'Deleting...' : 'Delete Account'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 653,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 644,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 623,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 622,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 263,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsPage, \"mJGh49gcpbDhwsBluK0ECgDdz60=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_8__.useSubscription\n    ];\n});\n_c = SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/settings/page.tsx\n"));

/***/ })

});