"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/settings/page",{

/***/ "(app-pages-browser)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SettingsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction SettingsPage() {\n    var _user_user_metadata, _user_user_metadata1, _user_user_metadata2;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_7__.useSubscription)();\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_6__.createSupabaseBrowserClient)();\n    const { success, error: toastError } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('account');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCurrentPassword, setShowCurrentPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNewPassword, setShowNewPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDeleteModal, setShowDeleteModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showResetModal, setShowResetModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isResetLoading, setIsResetLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showChangeEmailModal, setShowChangeEmailModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEmailLoading, setIsEmailLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [emailChangeData, setEmailChangeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        newEmail: '',\n        currentPassword: ''\n    });\n    const [passwordData, setPasswordData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n    });\n    const [notificationSettings, setNotificationSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        emailNotifications: true,\n        securityAlerts: true,\n        usageAlerts: true,\n        marketingEmails: false\n    });\n    const [userStats, setUserStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        configCount: 0,\n        apiKeyCount: 0,\n        userApiKeyCount: 0\n    });\n    const sidebarItems = [\n        {\n            id: 'account',\n            label: 'Account settings',\n            icon: _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            id: 'notifications',\n            label: 'Notifications',\n            icon: _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            id: 'security',\n            label: 'Security',\n            icon: _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            id: 'danger',\n            label: 'Danger zone',\n            icon: _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        }\n    ];\n    // Load user data and stats\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsPage.useEffect\": ()=>{\n            if (user) {\n                loadUserStats();\n                loadNotificationSettings();\n            }\n        }\n    }[\"SettingsPage.useEffect\"], [\n        user\n    ]);\n    const loadUserStats = async ()=>{\n        try {\n            // Get configuration count\n            const configResponse = await fetch('/api/custom-configs');\n            const configData = configResponse.ok ? await configResponse.json() : [];\n            const configs = Array.isArray(configData) ? configData : [];\n            // Get user API keys count\n            const userKeysResponse = await fetch('/api/user-api-keys');\n            const userKeysData = userKeysResponse.ok ? await userKeysResponse.json() : {\n                api_keys: []\n            };\n            const userKeys = userKeysData.api_keys || [];\n            // Get total API keys count across all configs\n            let totalApiKeys = 0;\n            for (const config of configs){\n                try {\n                    const keysResponse = await fetch(\"/api/custom-configs/\".concat(config.id, \"/keys\"));\n                    const keysData = keysResponse.ok ? await keysResponse.json() : {\n                        api_keys: []\n                    };\n                    totalApiKeys += (keysData.api_keys || []).length;\n                } catch (error) {\n                    console.error('Error loading keys for config:', config.id, error);\n                }\n            }\n            setUserStats({\n                configCount: configs.length,\n                apiKeyCount: totalApiKeys,\n                userApiKeyCount: userKeys.length\n            });\n        } catch (error) {\n            console.error('Error loading user stats:', error);\n        }\n    };\n    const loadNotificationSettings = async ()=>{\n        try {\n            // Load from user metadata or local storage\n            const savedSettings = localStorage.getItem('notification_settings');\n            if (savedSettings) {\n                setNotificationSettings(JSON.parse(savedSettings));\n            }\n        } catch (error) {\n            console.error('Error loading notification settings:', error);\n        }\n    };\n    const handlePasswordChange = async (e)=>{\n        e.preventDefault();\n        if (!passwordData.currentPassword.trim()) {\n            toastError('Current password is required');\n            return;\n        }\n        if (!passwordData.newPassword.trim()) {\n            toastError('New password is required');\n            return;\n        }\n        if (passwordData.newPassword !== passwordData.confirmPassword) {\n            toastError('New passwords do not match');\n            return;\n        }\n        if (passwordData.newPassword.length < 8) {\n            toastError('Password must be at least 8 characters long');\n            return;\n        }\n        if (passwordData.newPassword === passwordData.currentPassword) {\n            toastError('New password must be different from current password');\n            return;\n        }\n        setLoading(true);\n        try {\n            // First verify current password by attempting to sign in\n            const { error: signInError } = await supabase.auth.signInWithPassword({\n                email: (user === null || user === void 0 ? void 0 : user.email) || '',\n                password: passwordData.currentPassword\n            });\n            if (signInError) {\n                toastError('Current password is incorrect');\n                setLoading(false);\n                return;\n            }\n            // If current password is correct, update to new password\n            const { error: updateError } = await supabase.auth.updateUser({\n                password: passwordData.newPassword\n            });\n            if (updateError) throw updateError;\n            success('Password updated successfully');\n            setPasswordData({\n                currentPassword: '',\n                newPassword: '',\n                confirmPassword: ''\n            });\n        } catch (error) {\n            console.error('Password update error:', error);\n            toastError('Failed to update password', error.message || 'Please try again');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleNotificationUpdate = async ()=>{\n        setLoading(true);\n        try {\n            // Save to local storage and user metadata\n            localStorage.setItem('notification_settings', JSON.stringify(notificationSettings));\n            // Also save to user metadata for persistence across devices\n            const { error } = await supabase.auth.updateUser({\n                data: {\n                    notification_preferences: notificationSettings\n                }\n            });\n            if (error) throw error;\n            success('Notification preferences saved successfully');\n        } catch (error) {\n            console.error('Notification update error:', error);\n            toastError('Failed to save notification preferences');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handlePasswordReset = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) {\n            toastError('No email address found');\n            return;\n        }\n        setIsResetLoading(true);\n        try {\n            const { error } = await supabase.auth.resetPasswordForEmail(user.email, {\n                redirectTo: \"\".concat(window.location.origin, \"/auth/reset-password\")\n            });\n            if (error) throw error;\n            success('Password reset email sent!', 'Check your inbox for instructions to reset your password.');\n            setShowResetModal(false);\n        } catch (error) {\n            console.error('Password reset error:', error);\n            toastError('Failed to send reset email', error.message || 'Please try again.');\n        } finally{\n            setIsResetLoading(false);\n        }\n    };\n    const handleEmailChange = async ()=>{\n        if (!emailChangeData.newEmail.trim()) {\n            toastError('Please enter a new email address');\n            return;\n        }\n        if (!emailChangeData.currentPassword.trim()) {\n            toastError('Current password is required to change email');\n            return;\n        }\n        if (emailChangeData.newEmail === (user === null || user === void 0 ? void 0 : user.email)) {\n            toastError('New email must be different from current email');\n            return;\n        }\n        // Basic email validation\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(emailChangeData.newEmail)) {\n            toastError('Please enter a valid email address');\n            return;\n        }\n        setIsEmailLoading(true);\n        try {\n            // First verify current password\n            const { error: signInError } = await supabase.auth.signInWithPassword({\n                email: (user === null || user === void 0 ? void 0 : user.email) || '',\n                password: emailChangeData.currentPassword\n            });\n            if (signInError) {\n                toastError('Current password is incorrect');\n                setIsEmailLoading(false);\n                return;\n            }\n            // Update email\n            const { error: updateError } = await supabase.auth.updateUser({\n                email: emailChangeData.newEmail\n            });\n            if (updateError) throw updateError;\n            success('Email change initiated!', 'Check both your old and new email addresses for confirmation instructions.');\n            setShowChangeEmailModal(false);\n            setEmailChangeData({\n                newEmail: '',\n                currentPassword: ''\n            });\n        } catch (error) {\n            console.error('Email change error:', error);\n            toastError('Failed to change email', error.message || 'Please try again.');\n        } finally{\n            setIsEmailLoading(false);\n        }\n    };\n    const handleAccountDeletion = async ()=>{\n        if (!user) return;\n        setLoading(true);\n        try {\n            // Call our account deletion API\n            const response = await fetch('/api/user/delete-account', {\n                method: 'DELETE',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || 'Failed to delete account');\n            }\n            toast.success('Account deleted successfully. You will be redirected to the homepage.');\n            // Sign out and redirect\n            await supabase.auth.signOut();\n            setTimeout(()=>{\n                window.location.href = '/';\n            }, 2000);\n        } catch (error) {\n            console.error('Account deletion error:', error);\n            toast.error(error.message || 'Failed to delete account. Please contact support.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Settings\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-1\",\n                            children: \"Manage your account settings and preferences\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 327,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"-mb-px flex space-x-8\",\n                    children: sidebarItems.map((item)=>{\n                        const Icon = item.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveSection(item.id),\n                            className: \"flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors \".concat(activeSection === item.id ? 'border-orange-500 text-orange-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 17\n                                }, this),\n                                item.label\n                            ]\n                        }, item.id, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 336,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 335,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl\",\n                children: [\n                    activeSection === 'account' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Email address\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: [\n                                                            \"Your email address is \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: user === null || user === void 0 ? void 0 : user.email\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 74\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 mt-1\",\n                                                        children: \"This is used for login and important notifications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-md transition-colors\",\n                                                onClick: ()=>setShowChangeEmailModal(true),\n                                                children: \"Change\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-6\",\n                                        children: \"Account Information\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-900 mb-1\",\n                                                                children: \"Full Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || (user === null || user === void 0 ? void 0 : (_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : _user_user_metadata1.first_name) + ' ' + (user === null || user === void 0 ? void 0 : (_user_user_metadata2 = user.user_metadata) === null || _user_user_metadata2 === void 0 ? void 0 : _user_user_metadata2.last_name) || 'Not set'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-900 mb-1\",\n                                                                children: \"Account Created\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: (user === null || user === void 0 ? void 0 : user.created_at) ? new Date(user.created_at).toLocaleDateString('en-US', {\n                                                                    year: 'numeric',\n                                                                    month: 'long',\n                                                                    day: 'numeric'\n                                                                }) : 'Unknown'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-gray-900 mb-1\",\n                                                            children: \"Account Usage\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1 text-sm text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        userStats.configCount,\n                                                                        \" configurations\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 404,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        userStats.apiKeyCount,\n                                                                        \" API keys\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 405,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        userStats.userApiKeyCount,\n                                                                        \" user-generated keys\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-6\",\n                                        children: \"Password\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handlePasswordChange,\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"currentPassword\",\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Current password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative mt-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                id: \"currentPassword\",\n                                                                type: showCurrentPassword ? \"text\" : \"password\",\n                                                                value: passwordData.currentPassword,\n                                                                onChange: (e)=>setPasswordData((prev)=>({\n                                                                            ...prev,\n                                                                            currentPassword: e.target.value\n                                                                        })),\n                                                                placeholder: \"••••••••••\",\n                                                                className: \"w-full pr-10 px-3 py-2 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 bg-white focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200\",\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 420,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>setShowCurrentPassword(!showCurrentPassword),\n                                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                                                children: showCurrentPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 50\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 89\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 429,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"newPassword\",\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"New password\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"newPassword\",\n                                                                        type: showNewPassword ? \"text\" : \"password\",\n                                                                        value: passwordData.newPassword,\n                                                                        onChange: (e)=>setPasswordData((prev)=>({\n                                                                                    ...prev,\n                                                                                    newPassword: e.target.value\n                                                                                })),\n                                                                        placeholder: \"••••••••••\",\n                                                                        className: \"w-full pr-10 px-3 py-2 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 bg-white focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 442,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>setShowNewPassword(!showNewPassword),\n                                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                                                        children: showNewPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 456,\n                                                                            columnNumber: 48\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 456,\n                                                                            columnNumber: 87\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 451,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"confirmPassword\",\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Confirm new password\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"confirmPassword\",\n                                                                        type: showConfirmPassword ? \"text\" : \"password\",\n                                                                        value: passwordData.confirmPassword,\n                                                                        onChange: (e)=>setPasswordData((prev)=>({\n                                                                                    ...prev,\n                                                                                    confirmPassword: e.target.value\n                                                                                })),\n                                                                        placeholder: \"••••••••••\",\n                                                                        className: \"w-full pr-10 px-3 py-2 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 bg-white focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 463,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>setShowConfirmPassword(!showConfirmPassword),\n                                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                                                        children: showConfirmPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 477,\n                                                                            columnNumber: 52\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 477,\n                                                                            columnNumber: 91\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 472,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            \"Can't remember your current password? \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                                                onClick: ()=>setShowResetModal(true),\n                                                                children: \"Reset your password\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 484,\n                                                                columnNumber: 63\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"submit\",\n                                                        disabled: loading,\n                                                        className: \"bg-orange-600 hover:bg-orange-700\",\n                                                        children: loading ? 'Saving...' : 'Save password'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 15\n                    }, this),\n                    activeSection === 'notifications' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-6\",\n                                    children: \"Notification Preferences\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        Object.entries(notificationSettings).map((param)=>{\n                                            let [key, value] = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: [\n                                                                    key === 'emailNotifications' && 'Email Notifications',\n                                                                    key === 'securityAlerts' && 'Security Alerts',\n                                                                    key === 'usageAlerts' && 'Usage Alerts',\n                                                                    key === 'marketingEmails' && 'Marketing Emails'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 509,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500 mt-1\",\n                                                                children: [\n                                                                    key === 'emailNotifications' && 'Receive general email notifications',\n                                                                    key === 'securityAlerts' && 'Get notified about security events',\n                                                                    key === 'usageAlerts' && 'Alerts about API usage and limits',\n                                                                    key === 'marketingEmails' && 'Product updates and promotional content'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: value,\n                                                                onChange: (e)=>setNotificationSettings((prev)=>({\n                                                                            ...prev,\n                                                                            [key]: e.target.checked\n                                                                        })),\n                                                                className: \"sr-only peer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 523,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 529,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, key, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 23\n                                            }, this);\n                                        }),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pt-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: handleNotificationUpdate,\n                                                disabled: loading,\n                                                className: \"bg-orange-600 hover:bg-orange-700\",\n                                                children: loading ? 'Saving...' : 'Save preferences'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 533,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 503,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 502,\n                        columnNumber: 15\n                    }, this),\n                    activeSection === 'security' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"Security Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 547,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Secure\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-900 mb-2\",\n                                                        children: \"Password Security\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-3\",\n                                                        children: \"Change your password regularly for better security\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setActiveSection('account'),\n                                                        className: \"text-orange-600 border-orange-300 hover:bg-orange-50\",\n                                                        children: \"Change Password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-gray-900 mb-1\",\n                                                            children: \"Account Activity\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                userStats.configCount + userStats.apiKeyCount + userStats.userApiKeyCount,\n                                                                \" total resources created\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 572,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-gray-900 mb-1\",\n                                                            children: \"Account Created\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: (user === null || user === void 0 ? void 0 : user.created_at) ? new Date(user.created_at).toLocaleDateString('en-US', {\n                                                                year: 'numeric',\n                                                                month: 'long',\n                                                                day: 'numeric'\n                                                            }) : 'Unknown'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 554,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 545,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 544,\n                        columnNumber: 15\n                    }, this),\n                    activeSection === 'danger' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg border border-red-200 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-6 w-6 text-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-red-900\",\n                                            children: \"Danger Zone\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 rounded-lg p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-medium text-red-900 mb-3\",\n                                            children: \"Delete Account\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-700 mb-4\",\n                                            children: \"Permanently delete your RouKey account and all associated data. This action cannot be undone.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white border border-red-200 rounded-md p-4 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"text-sm font-medium text-red-900 mb-2\",\n                                                    children: \"This will permanently delete:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 606,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-sm text-red-700 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                \"• \",\n                                                                userStats.configCount,\n                                                                \" API configurations\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 608,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                \"• \",\n                                                                userStats.apiKeyCount,\n                                                                \" API keys\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 609,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                \"• \",\n                                                                userStats.userApiKeyCount,\n                                                                \" user-generated API keys\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 610,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• All usage logs and analytics\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Your subscription and billing information\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 612,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 607,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 605,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            className: \"text-red-600 border-red-300 hover:bg-red-100 hover:border-red-400\",\n                                            onClick: ()=>setShowDeleteModal(true),\n                                            disabled: loading,\n                                            children: \"Delete my account\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 615,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 600,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 593,\n                        columnNumber: 15\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 358,\n                columnNumber: 7\n            }, this),\n            showChangeEmailModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-2xl max-w-md w-full p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5 text-orange-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 635,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 634,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Change Email Address\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 637,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 633,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"Enter your new email address and current password to change your email. You'll need to verify both your old and new email addresses.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 640,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: (e)=>{\n                                e.preventDefault();\n                                handleEmailChange();\n                            },\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"newEmail\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"New Email Address\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 647,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"newEmail\",\n                                            type: \"email\",\n                                            value: emailChangeData.newEmail,\n                                            onChange: (e)=>setEmailChangeData((prev)=>({\n                                                        ...prev,\n                                                        newEmail: e.target.value\n                                                    })),\n                                            placeholder: \"Enter your new email address\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 650,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 646,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"currentPasswordEmail\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Current Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 662,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"currentPasswordEmail\",\n                                            type: \"password\",\n                                            value: emailChangeData.currentPassword,\n                                            onChange: (e)=>setEmailChangeData((prev)=>({\n                                                        ...prev,\n                                                        currentPassword: e.target.value\n                                                    })),\n                                            placeholder: \"Enter your current password\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 665,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 661,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-orange-50 border border-orange-200 rounded-lg p-4 mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium text-orange-900 mb-2\",\n                                            children: \"What happens next:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 677,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm text-orange-700 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• You'll receive confirmation emails at both addresses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Click the links in both emails to confirm the change\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 680,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Your email will be updated once both are verified\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 681,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• You can continue using your current email until then\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 682,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 678,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 676,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>{\n                                                setShowChangeEmailModal(false);\n                                                setEmailChangeData({\n                                                    newEmail: '',\n                                                    currentPassword: ''\n                                                });\n                                            },\n                                            className: \"flex-1\",\n                                            disabled: isEmailLoading,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 687,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"submit\",\n                                            disabled: isEmailLoading,\n                                            className: \"flex-1 bg-orange-600 hover:bg-orange-700 text-white\",\n                                            children: isEmailLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 706,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Changing...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 21\n                                            }, this) : 'Change Email'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 699,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 686,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 645,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 632,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 631,\n                columnNumber: 9\n            }, this),\n            showResetModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-2xl max-w-md w-full p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 725,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 724,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Reset Your Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 727,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 723,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: [\n                                \"We'll send a password reset link to \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: user === null || user === void 0 ? void 0 : user.email\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 731,\n                                    columnNumber: 51\n                                }, this),\n                                \". You can use this link to create a new password.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 730,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-blue-900 mb-2\",\n                                    children: \"What happens next:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 736,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-blue-700 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• You'll receive an email with a secure reset link\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 738,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Click the link to create a new password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 739,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Your current password will remain active until you complete the reset\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 740,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 737,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 735,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowResetModal(false),\n                                    className: \"flex-1\",\n                                    disabled: isResetLoading,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 745,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handlePasswordReset,\n                                    disabled: isResetLoading,\n                                    className: \"flex-1 bg-blue-600 hover:bg-blue-700 text-white\",\n                                    children: isResetLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 760,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Sending...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 759,\n                                        columnNumber: 19\n                                    }, this) : 'Send Reset Link'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 753,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 744,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 722,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 721,\n                columnNumber: 9\n            }, this),\n            showDeleteModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-2xl max-w-md w-full p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-6 w-6 text-red-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 777,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Delete Account\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 778,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 776,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Are you absolutely sure you want to delete your account? This action cannot be undone.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 781,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-red-900 mb-2\",\n                                    children: \"This will permanently delete:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 786,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-red-700 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                userStats.configCount,\n                                                \" API configurations\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 788,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                userStats.apiKeyCount,\n                                                \" API keys\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 789,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                userStats.userApiKeyCount,\n                                                \" user-generated API keys\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 790,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• All usage logs and analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 791,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Your subscription and billing information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 792,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 787,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 785,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowDeleteModal(false),\n                                    className: \"flex-1\",\n                                    disabled: loading,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 797,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleAccountDeletion,\n                                    disabled: loading,\n                                    className: \"flex-1 bg-red-600 hover:bg-red-700 text-white\",\n                                    children: loading ? 'Deleting...' : 'Delete Account'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 805,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 796,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 775,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 774,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 325,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsPage, \"mrO/1whaEDYY8En15/HfYhebKMM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_7__.useSubscription,\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/settings/page.tsx\n"));

/***/ })

});