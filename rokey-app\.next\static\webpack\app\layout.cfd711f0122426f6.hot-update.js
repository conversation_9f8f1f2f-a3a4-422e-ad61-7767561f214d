"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"cd6ee3f8ebf3\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImNkNmVlM2Y4ZWJmM1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/LayoutContent.tsx":
/*!******************************************!*\
  !*** ./src/components/LayoutContent.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LayoutContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navbar */ \"(app-pages-browser)/./src/components/Navbar.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar.tsx\");\n/* harmony import */ var _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/SidebarContext */ \"(app-pages-browser)/./src/contexts/SidebarContext.tsx\");\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _components_OptimisticLoadingScreen__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/OptimisticLoadingScreen */ \"(app-pages-browser)/./src/components/OptimisticLoadingScreen.tsx\");\n/* harmony import */ var _components_OptimisticPageLoader__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/OptimisticPageLoader */ \"(app-pages-browser)/./src/components/OptimisticPageLoader.tsx\");\n/* harmony import */ var _hooks_useAdvancedPreloading__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useAdvancedPreloading */ \"(app-pages-browser)/./src/hooks/useAdvancedPreloading.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction LayoutContentInner(param) {\n    let { children } = param;\n    _s();\n    const { isCollapsed, isHovered, collapseSidebar } = (0,_contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_4__.useSidebar)();\n    const navigationContext = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_5__.useNavigationSafe)();\n    const { isNavigating, targetRoute, isPageCached } = navigationContext || {\n        isNavigating: false,\n        targetRoute: null,\n        isPageCached: ()=>false\n    };\n    // Track if we're on desktop (lg breakpoint and above)\n    const [isDesktop, setIsDesktop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LayoutContentInner.useEffect\": ()=>{\n            const checkIsDesktop = {\n                \"LayoutContentInner.useEffect.checkIsDesktop\": ()=>{\n                    setIsDesktop(window.innerWidth >= 1024); // lg breakpoint\n                }\n            }[\"LayoutContentInner.useEffect.checkIsDesktop\"];\n            // Check on mount\n            checkIsDesktop();\n            // Listen for resize events\n            window.addEventListener('resize', checkIsDesktop);\n            return ({\n                \"LayoutContentInner.useEffect\": ()=>window.removeEventListener('resize', checkIsDesktop)\n            })[\"LayoutContentInner.useEffect\"];\n        }\n    }[\"LayoutContentInner.useEffect\"], []);\n    // Calculate actual sidebar width based on collapsed and hover states\n    // Only apply sidebar width on desktop, mobile uses overlay\n    const sidebarWidth = isDesktop ? !isCollapsed || isHovered ? 256 : 64 : 0;\n    // Initialize advanced preloading system\n    (0,_hooks_useAdvancedPreloading__WEBPACK_IMPORTED_MODULE_8__.useAdvancedPreloading)({\n        maxConcurrent: 2,\n        idleTimeout: 1500,\n        backgroundDelay: 3000\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:block fixed left-0 top-0 h-full z-40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden fixed inset-0 z-50 \".concat(isCollapsed ? 'pointer-events-none' : ''),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onClick: collapseSidebar,\n                        className: \"absolute inset-0 bg-black transition-opacity duration-200 ease-out cursor-pointer \".concat(isCollapsed ? 'opacity-0' : 'opacity-50')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute left-0 top-0 h-full transform transition-transform duration-200 ease-out \".concat(isCollapsed ? '-translate-x-full' : 'translate-x-0'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden min-w-0 transition-all duration-200 ease-out\",\n                style: {\n                    marginLeft: \"\".concat(sidebarWidth, \"px\")\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed top-0 right-0 z-30 transition-all duration-200 ease-out\",\n                        style: {\n                            left: \"\".concat(sidebarWidth, \"px\")\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-y-auto content-area mt-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 sm:p-6 lg:p-8 w-full \".concat(// When sidebar is expanded, use standard max width with centering\n                            // When sidebar is collapsed, use larger max width or no max width\n                            isDesktop && (!isCollapsed || isHovered) ? 'max-w-7xl mx-auto' : isDesktop ? 'max-w-none px-8' : 'max-w-7xl mx-auto'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"page-transition\",\n                                children: isNavigating && targetRoute ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OptimisticPageLoader__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    targetRoute: targetRoute,\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 17\n                                }, this) : children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n_s(LayoutContentInner, \"ZyCqd3IdScc5tgkO1NQxE8QAdJg=\", false, function() {\n    return [\n        _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_4__.useSidebar,\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_5__.useNavigationSafe,\n        _hooks_useAdvancedPreloading__WEBPACK_IMPORTED_MODULE_8__.useAdvancedPreloading\n    ];\n});\n_c = LayoutContentInner;\nfunction LayoutContent(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OptimisticLoadingScreen__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            targetRoute: null\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n            lineNumber: 120,\n            columnNumber: 25\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LayoutContentInner, {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n            lineNumber: 121,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n_c1 = LayoutContent;\nvar _c, _c1;\n$RefreshReg$(_c, \"LayoutContentInner\");\n$RefreshReg$(_c1, \"LayoutContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0xheW91dENvbnRlbnQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFc0Q7QUFDYjtBQUNFO0FBQ1k7QUFDVTtBQUNVO0FBQ047QUFDQztBQUd0RSxTQUFTVSxtQkFBbUIsS0FBMkM7UUFBM0MsRUFBRUMsUUFBUSxFQUFpQyxHQUEzQzs7SUFDMUIsTUFBTSxFQUFFQyxXQUFXLEVBQUVDLFNBQVMsRUFBRUMsZUFBZSxFQUFFLEdBQUdULG9FQUFVQTtJQUM5RCxNQUFNVSxvQkFBb0JULDhFQUFpQkE7SUFDM0MsTUFBTSxFQUFFVSxZQUFZLEVBQUVDLFdBQVcsRUFBRUMsWUFBWSxFQUFFLEdBQUdILHFCQUFxQjtRQUN2RUMsY0FBYztRQUNkQyxhQUFhO1FBQ2JDLGNBQWMsSUFBTTtJQUN0QjtJQUVBLHNEQUFzRDtJQUN0RCxNQUFNLENBQUNDLFdBQVdDLGFBQWEsR0FBR25CLCtDQUFRQSxDQUFDO0lBRTNDQyxnREFBU0E7d0NBQUM7WUFDUixNQUFNbUI7K0RBQWlCO29CQUNyQkQsYUFBYUUsT0FBT0MsVUFBVSxJQUFJLE9BQU8sZ0JBQWdCO2dCQUMzRDs7WUFFQSxpQkFBaUI7WUFDakJGO1lBRUEsMkJBQTJCO1lBQzNCQyxPQUFPRSxnQkFBZ0IsQ0FBQyxVQUFVSDtZQUNsQztnREFBTyxJQUFNQyxPQUFPRyxtQkFBbUIsQ0FBQyxVQUFVSjs7UUFDcEQ7dUNBQUcsRUFBRTtJQUVMLHFFQUFxRTtJQUNyRSwyREFBMkQ7SUFDM0QsTUFBTUssZUFBZVAsWUFBYSxDQUFFUCxlQUFlQyxZQUFhLE1BQU0sS0FBTTtJQUU1RSx3Q0FBd0M7SUFDeENKLG1GQUFxQkEsQ0FBQztRQUNwQmtCLGVBQWU7UUFDZkMsYUFBYTtRQUNiQyxpQkFBaUI7SUFDbkI7SUFFQSxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDM0IsMkRBQU9BOzs7Ozs7Ozs7OzBCQUlWLDhEQUFDMEI7Z0JBQUlDLFdBQVcsZ0NBQXlFLE9BQXpDbkIsY0FBYyx3QkFBd0I7O2tDQUVwRiw4REFBQ2tCO3dCQUNDRSxTQUFTbEI7d0JBQ1RpQixXQUFXLHFGQUVWLE9BRENuQixjQUFjLGNBQWM7Ozs7OztrQ0FLaEMsOERBQUNrQjt3QkFBSUMsV0FBVyxxRkFFZixPQURDbkIsY0FBYyxzQkFBc0I7a0NBRXBDLDRFQUFDUiwyREFBT0E7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBS1osOERBQUMwQjtnQkFDQ0MsV0FBVTtnQkFDVkUsT0FBTztvQkFDTEMsWUFBWSxHQUFnQixPQUFiUixjQUFhO2dCQUM5Qjs7a0NBR0EsOERBQUNJO3dCQUNDQyxXQUFVO3dCQUNWRSxPQUFPOzRCQUFFRSxNQUFNLEdBQWdCLE9BQWJULGNBQWE7d0JBQUk7a0NBRW5DLDRFQUFDdkIsMERBQU1BOzs7Ozs7Ozs7O2tDQUlULDhEQUFDaUM7d0JBQUtMLFdBQVU7a0NBQ2QsNEVBQUNEOzRCQUFJQyxXQUFXLDRCQVFmLE9BUEMsa0VBQWtFOzRCQUNsRSxrRUFBa0U7NEJBQ2xFWixhQUFjLEVBQUNQLGVBQWVDLFNBQVEsSUFDbEMsc0JBQ0FNLFlBQ0Usb0JBQ0E7c0NBRU4sNEVBQUNXO2dDQUFJQyxXQUFVOzBDQUNaZixnQkFBZ0JDLDRCQUNmLDhEQUFDVCx3RUFBb0JBO29DQUFDUyxhQUFhQTs4Q0FDaENOOzs7OzsyQ0FHSEE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFVaEI7R0F2R1NEOztRQUM2Q0wsZ0VBQVVBO1FBQ3BDQywwRUFBaUJBO1FBNEIzQ0csK0VBQXFCQTs7O0tBOUJkQztBQXlHTSxTQUFTMkIsY0FBYyxLQUEyQztRQUEzQyxFQUFFMUIsUUFBUSxFQUFpQyxHQUEzQztJQUNwQyxxQkFDRSw4REFBQ1gsMkNBQVFBO1FBQUNzQyx3QkFBVSw4REFBQy9CLDJFQUF1QkE7WUFBQ1UsYUFBYTs7Ozs7O2tCQUN4RCw0RUFBQ1A7c0JBQW9CQzs7Ozs7Ozs7Ozs7QUFHM0I7TUFOd0IwQiIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxzcmNcXGNvbXBvbmVudHNcXExheW91dENvbnRlbnQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgU3VzcGVuc2UsIHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCBOYXZiYXIgZnJvbSBcIkAvY29tcG9uZW50cy9OYXZiYXJcIjtcbmltcG9ydCBTaWRlYmFyIGZyb20gXCJAL2NvbXBvbmVudHMvU2lkZWJhclwiO1xuaW1wb3J0IHsgdXNlU2lkZWJhciB9IGZyb20gXCJAL2NvbnRleHRzL1NpZGViYXJDb250ZXh0XCI7XG5pbXBvcnQgeyB1c2VOYXZpZ2F0aW9uU2FmZSB9IGZyb20gXCJAL2NvbnRleHRzL05hdmlnYXRpb25Db250ZXh0XCI7XG5pbXBvcnQgT3B0aW1pc3RpY0xvYWRpbmdTY3JlZW4gZnJvbSBcIkAvY29tcG9uZW50cy9PcHRpbWlzdGljTG9hZGluZ1NjcmVlblwiO1xuaW1wb3J0IE9wdGltaXN0aWNQYWdlTG9hZGVyIGZyb20gXCJAL2NvbXBvbmVudHMvT3B0aW1pc3RpY1BhZ2VMb2FkZXJcIjtcbmltcG9ydCB7IHVzZUFkdmFuY2VkUHJlbG9hZGluZyB9IGZyb20gXCJAL2hvb2tzL3VzZUFkdmFuY2VkUHJlbG9hZGluZ1wiO1xuaW1wb3J0IHsgdXNlVG9hc3QsIFRvYXN0Q29udGFpbmVyIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9Ub2FzdFwiO1xuXG5mdW5jdGlvbiBMYXlvdXRDb250ZW50SW5uZXIoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICBjb25zdCB7IGlzQ29sbGFwc2VkLCBpc0hvdmVyZWQsIGNvbGxhcHNlU2lkZWJhciB9ID0gdXNlU2lkZWJhcigpO1xuICBjb25zdCBuYXZpZ2F0aW9uQ29udGV4dCA9IHVzZU5hdmlnYXRpb25TYWZlKCk7XG4gIGNvbnN0IHsgaXNOYXZpZ2F0aW5nLCB0YXJnZXRSb3V0ZSwgaXNQYWdlQ2FjaGVkIH0gPSBuYXZpZ2F0aW9uQ29udGV4dCB8fCB7XG4gICAgaXNOYXZpZ2F0aW5nOiBmYWxzZSxcbiAgICB0YXJnZXRSb3V0ZTogbnVsbCxcbiAgICBpc1BhZ2VDYWNoZWQ6ICgpID0+IGZhbHNlXG4gIH07XG5cbiAgLy8gVHJhY2sgaWYgd2UncmUgb24gZGVza3RvcCAobGcgYnJlYWtwb2ludCBhbmQgYWJvdmUpXG4gIGNvbnN0IFtpc0Rlc2t0b3AsIHNldElzRGVza3RvcF0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBjaGVja0lzRGVza3RvcCA9ICgpID0+IHtcbiAgICAgIHNldElzRGVza3RvcCh3aW5kb3cuaW5uZXJXaWR0aCA+PSAxMDI0KTsgLy8gbGcgYnJlYWtwb2ludFxuICAgIH07XG5cbiAgICAvLyBDaGVjayBvbiBtb3VudFxuICAgIGNoZWNrSXNEZXNrdG9wKCk7XG5cbiAgICAvLyBMaXN0ZW4gZm9yIHJlc2l6ZSBldmVudHNcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgY2hlY2tJc0Rlc2t0b3ApO1xuICAgIHJldHVybiAoKSA9PiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigncmVzaXplJywgY2hlY2tJc0Rlc2t0b3ApO1xuICB9LCBbXSk7XG5cbiAgLy8gQ2FsY3VsYXRlIGFjdHVhbCBzaWRlYmFyIHdpZHRoIGJhc2VkIG9uIGNvbGxhcHNlZCBhbmQgaG92ZXIgc3RhdGVzXG4gIC8vIE9ubHkgYXBwbHkgc2lkZWJhciB3aWR0aCBvbiBkZXNrdG9wLCBtb2JpbGUgdXNlcyBvdmVybGF5XG4gIGNvbnN0IHNpZGViYXJXaWR0aCA9IGlzRGVza3RvcCA/ICgoIWlzQ29sbGFwc2VkIHx8IGlzSG92ZXJlZCkgPyAyNTYgOiA2NCkgOiAwO1xuXG4gIC8vIEluaXRpYWxpemUgYWR2YW5jZWQgcHJlbG9hZGluZyBzeXN0ZW1cbiAgdXNlQWR2YW5jZWRQcmVsb2FkaW5nKHtcbiAgICBtYXhDb25jdXJyZW50OiAyLFxuICAgIGlkbGVUaW1lb3V0OiAxNTAwLFxuICAgIGJhY2tncm91bmREZWxheTogMzAwMFxuICB9KTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLXNjcmVlbiBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgIHsvKiBEZXNrdG9wIFNpZGViYXIgLSBGaXhlZCBQb3NpdGlvbiAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIGxnOmJsb2NrIGZpeGVkIGxlZnQtMCB0b3AtMCBoLWZ1bGwgei00MFwiPlxuICAgICAgICA8U2lkZWJhciAvPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBNb2JpbGUgU2lkZWJhciBPdmVybGF5ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9e2BsZzpoaWRkZW4gZml4ZWQgaW5zZXQtMCB6LTUwICR7aXNDb2xsYXBzZWQgPyAncG9pbnRlci1ldmVudHMtbm9uZScgOiAnJ31gfT5cbiAgICAgICAgey8qIEJhY2tkcm9wICovfVxuICAgICAgICA8ZGl2XG4gICAgICAgICAgb25DbGljaz17Y29sbGFwc2VTaWRlYmFyfVxuICAgICAgICAgIGNsYXNzTmFtZT17YGFic29sdXRlIGluc2V0LTAgYmctYmxhY2sgdHJhbnNpdGlvbi1vcGFjaXR5IGR1cmF0aW9uLTIwMCBlYXNlLW91dCBjdXJzb3ItcG9pbnRlciAke1xuICAgICAgICAgICAgaXNDb2xsYXBzZWQgPyAnb3BhY2l0eS0wJyA6ICdvcGFjaXR5LTUwJ1xuICAgICAgICAgIH1gfVxuICAgICAgICAvPlxuXG4gICAgICAgIHsvKiBTaWRlYmFyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGFic29sdXRlIGxlZnQtMCB0b3AtMCBoLWZ1bGwgdHJhbnNmb3JtIHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTIwMCBlYXNlLW91dCAke1xuICAgICAgICAgIGlzQ29sbGFwc2VkID8gJy10cmFuc2xhdGUteC1mdWxsJyA6ICd0cmFuc2xhdGUteC0wJ1xuICAgICAgICB9YH0+XG4gICAgICAgICAgPFNpZGViYXIgLz5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIE1haW4gY29udGVudCBhcmVhIC0gQWRqdXN0ZWQgZm9yIGZpeGVkIHNpZGViYXIgKi99XG4gICAgICA8ZGl2XG4gICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBmbGV4IGZsZXgtY29sIG92ZXJmbG93LWhpZGRlbiBtaW4tdy0wIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBlYXNlLW91dFwiXG4gICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgbWFyZ2luTGVmdDogYCR7c2lkZWJhcldpZHRofXB4YFxuICAgICAgICB9fVxuICAgICAgPlxuICAgICAgICB7LyogRml4ZWQgVG9wIE5hdmlnYXRpb24gKi99XG4gICAgICAgIDxkaXZcbiAgICAgICAgICBjbGFzc05hbWU9XCJmaXhlZCB0b3AtMCByaWdodC0wIHotMzAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGVhc2Utb3V0XCJcbiAgICAgICAgICBzdHlsZT17eyBsZWZ0OiBgJHtzaWRlYmFyV2lkdGh9cHhgIH19XG4gICAgICAgID5cbiAgICAgICAgICA8TmF2YmFyIC8+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBNYWluIHNjcm9sbGFibGUgY29udGVudCB3aXRoIHRvcCBtYXJnaW4gZm9yIGZpeGVkIG5hdmJhciAqL31cbiAgICAgICAgPG1haW4gY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LXktYXV0byBjb250ZW50LWFyZWEgbXQtMTZcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHAtNCBzbTpwLTYgbGc6cC04IHctZnVsbCAke1xuICAgICAgICAgICAgLy8gV2hlbiBzaWRlYmFyIGlzIGV4cGFuZGVkLCB1c2Ugc3RhbmRhcmQgbWF4IHdpZHRoIHdpdGggY2VudGVyaW5nXG4gICAgICAgICAgICAvLyBXaGVuIHNpZGViYXIgaXMgY29sbGFwc2VkLCB1c2UgbGFyZ2VyIG1heCB3aWR0aCBvciBubyBtYXggd2lkdGhcbiAgICAgICAgICAgIGlzRGVza3RvcCAmJiAoIWlzQ29sbGFwc2VkIHx8IGlzSG92ZXJlZClcbiAgICAgICAgICAgICAgPyAnbWF4LXctN3hsIG14LWF1dG8nXG4gICAgICAgICAgICAgIDogaXNEZXNrdG9wXG4gICAgICAgICAgICAgICAgPyAnbWF4LXctbm9uZSBweC04J1xuICAgICAgICAgICAgICAgIDogJ21heC13LTd4bCBteC1hdXRvJ1xuICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicGFnZS10cmFuc2l0aW9uXCI+XG4gICAgICAgICAgICAgIHtpc05hdmlnYXRpbmcgJiYgdGFyZ2V0Um91dGUgPyAoXG4gICAgICAgICAgICAgICAgPE9wdGltaXN0aWNQYWdlTG9hZGVyIHRhcmdldFJvdXRlPXt0YXJnZXRSb3V0ZX0+XG4gICAgICAgICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgICAgICAgPC9PcHRpbWlzdGljUGFnZUxvYWRlcj5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICBjaGlsZHJlblxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvbWFpbj5cbiAgICAgIDwvZGl2PlxuXG5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTGF5b3V0Q29udGVudCh7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XG4gIHJldHVybiAoXG4gICAgPFN1c3BlbnNlIGZhbGxiYWNrPXs8T3B0aW1pc3RpY0xvYWRpbmdTY3JlZW4gdGFyZ2V0Um91dGU9e251bGx9IC8+fT5cbiAgICAgIDxMYXlvdXRDb250ZW50SW5uZXI+e2NoaWxkcmVufTwvTGF5b3V0Q29udGVudElubmVyPlxuICAgIDwvU3VzcGVuc2U+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiU3VzcGVuc2UiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIk5hdmJhciIsIlNpZGViYXIiLCJ1c2VTaWRlYmFyIiwidXNlTmF2aWdhdGlvblNhZmUiLCJPcHRpbWlzdGljTG9hZGluZ1NjcmVlbiIsIk9wdGltaXN0aWNQYWdlTG9hZGVyIiwidXNlQWR2YW5jZWRQcmVsb2FkaW5nIiwiTGF5b3V0Q29udGVudElubmVyIiwiY2hpbGRyZW4iLCJpc0NvbGxhcHNlZCIsImlzSG92ZXJlZCIsImNvbGxhcHNlU2lkZWJhciIsIm5hdmlnYXRpb25Db250ZXh0IiwiaXNOYXZpZ2F0aW5nIiwidGFyZ2V0Um91dGUiLCJpc1BhZ2VDYWNoZWQiLCJpc0Rlc2t0b3AiLCJzZXRJc0Rlc2t0b3AiLCJjaGVja0lzRGVza3RvcCIsIndpbmRvdyIsImlubmVyV2lkdGgiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsInNpZGViYXJXaWR0aCIsIm1heENvbmN1cnJlbnQiLCJpZGxlVGltZW91dCIsImJhY2tncm91bmREZWxheSIsImRpdiIsImNsYXNzTmFtZSIsIm9uQ2xpY2siLCJzdHlsZSIsIm1hcmdpbkxlZnQiLCJsZWZ0IiwibWFpbiIsIkxheW91dENvbnRlbnQiLCJmYWxsYmFjayJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LayoutContent.tsx\n"));

/***/ })

});