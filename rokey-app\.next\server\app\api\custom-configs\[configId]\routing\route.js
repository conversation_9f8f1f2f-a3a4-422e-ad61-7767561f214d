/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/custom-configs/[configId]/routing/route";
exports.ids = ["app/api/custom-configs/[configId]/routing/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcustom-configs%2F%5BconfigId%5D%2Frouting%2Froute&page=%2Fapi%2Fcustom-configs%2F%5BconfigId%5D%2Frouting%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustom-configs%2F%5BconfigId%5D%2Frouting%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcustom-configs%2F%5BconfigId%5D%2Frouting%2Froute&page=%2Fapi%2Fcustom-configs%2F%5BconfigId%5D%2Frouting%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustom-configs%2F%5BconfigId%5D%2Frouting%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_custom_configs_configId_routing_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/custom-configs/[configId]/routing/route.ts */ \"(rsc)/./src/app/api/custom-configs/[configId]/routing/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/custom-configs/[configId]/routing/route\",\n        pathname: \"/api/custom-configs/[configId]/routing\",\n        filename: \"route\",\n        bundlePath: \"app/api/custom-configs/[configId]/routing/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\custom-configs\\\\[configId]\\\\routing\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_custom_configs_configId_routing_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcustom-configs%2F%5BconfigId%5D%2Frouting%2Froute&page=%2Fapi%2Fcustom-configs%2F%5BconfigId%5D%2Frouting%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustom-configs%2F%5BconfigId%5D%2Frouting%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/custom-configs/[configId]/routing/route.ts":
/*!****************************************************************!*\
  !*** ./src/app/api/custom-configs/[configId]/routing/route.ts ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stripe-client */ \"(rsc)/./src/lib/stripe-client.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\n\n// Define the allowed routing strategy types\nconst ALLOWED_ROUTING_STRATEGIES = [\n    'none',\n    'intelligent_role',\n    'complexity_round_robin',\n    'auto_optimal',\n    'strict_fallback',\n    'cost_optimized',\n    'ab_routing'\n];\nconst RoutingStrategyEnum = zod__WEBPACK_IMPORTED_MODULE_3__.z[\"enum\"](ALLOWED_ROUTING_STRATEGIES);\nconst UpdateRoutingSettingsSchema = zod__WEBPACK_IMPORTED_MODULE_3__.z.object({\n    routing_strategy: RoutingStrategyEnum,\n    routing_strategy_params: zod__WEBPACK_IMPORTED_MODULE_3__.z.record(zod__WEBPACK_IMPORTED_MODULE_3__.z.any()).nullable().optional()\n});\nasync function PUT(request, { params }) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    const { configId } = await params;\n    // Get authenticated user from session\n    const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n    if (sessionError || !session?.user) {\n        console.error('Authentication error in PUT routing settings:', sessionError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Unauthorized: You must be logged in to update routing settings.'\n        }, {\n            status: 401\n        });\n    }\n    if (!configId || typeof configId !== 'string') {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Invalid configuration ID.'\n        }, {\n            status: 400\n        });\n    }\n    let requestBody;\n    try {\n        requestBody = await request.json();\n    } catch (e) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Invalid JSON request body.'\n        }, {\n            status: 400\n        });\n    }\n    const validationResult = UpdateRoutingSettingsSchema.safeParse(requestBody);\n    if (!validationResult.success) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Invalid request body.',\n            issues: validationResult.error.flatten().fieldErrors\n        }, {\n            status: 400\n        });\n    }\n    const { routing_strategy, routing_strategy_params } = validationResult.data;\n    try {\n        // Check user's subscription tier for advanced routing access\n        const { data: subscription } = await supabase.from('subscriptions').select('tier').eq('user_id', session.user.id).eq('status', 'active').single();\n        const userTier = subscription?.tier || 'free';\n        // Define which strategies require advanced routing access\n        const advancedStrategies = [\n            'intelligent_role',\n            'complexity_round_robin',\n            'auto_optimal',\n            'cost_optimized',\n            'ab_routing'\n        ];\n        // Free tier can only use 'none' and 'strict_fallback'\n        if (userTier === 'free' && advancedStrategies.includes(routing_strategy)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `The ${routing_strategy} routing strategy is not available on the free plan. Please upgrade to access advanced routing strategies.`\n            }, {\n                status: 403\n            });\n        }\n        // Check if user has access to advanced routing features\n        if (advancedStrategies.includes(routing_strategy) && !(0,_lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__.hasFeatureAccess)(userTier, 'advanced_routing')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Advanced routing strategies are not available on the ${userTier} plan. Please upgrade to access this feature.`\n            }, {\n                status: 403\n            });\n        }\n        // Verify the user owns the custom_api_config or is an admin (for future use)\n        // const { data: configData, error: configError } = await supabase\n        //   .from('custom_api_configs')\n        //   .select('id, user_id')\n        //   .eq('id', configId)\n        //   .eq('user_id', user.id) // Ensure the authenticated user owns this config\n        //   .single();\n        // if (configError || !configData) {\n        //   if (configError && configError.code === 'PGRST116') { // No rows found\n        //     return NextResponse.json({ error: 'Configuration not found or you do not have permission to modify it.' }, { status: 404 });\n        //   }\n        //   console.error('Error fetching config for permission check:', configError);\n        //   return NextResponse.json({ error: 'Error verifying configuration ownership.' }, { status: 500 });\n        // }\n        // Perform the update\n        const { error: updateError } = await supabase.from('custom_api_configs').update({\n            routing_strategy: routing_strategy,\n            routing_strategy_params: routing_strategy_params,\n            updated_at: new Date().toISOString()\n        }).eq('id', configId).eq('user_id', session.user.id); // Ensure user owns the config for RLS compliance\n        if (updateError) {\n            console.error('Error updating routing settings in Supabase:', updateError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to update routing settings.',\n                details: updateError.message\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Routing settings updated successfully.',\n            routing_strategy,\n            routing_strategy_params\n        }, {\n            status: 200\n        });\n    } catch (err) {\n        console.error('Unexpected error in PUT /custom-configs/[configId]/routing:', err);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected server error occurred.',\n            details: err.message\n        }, {\n            status: 500\n        });\n    }\n}\n// Optional: OPTIONS handler for CORS preflight if your frontend is on a different domain/port during dev\nasync function OPTIONS() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({}, {\n        status: 200,\n        headers: {\n            'Access-Control-Allow-Origin': '*',\n            'Access-Control-Allow-Methods': 'PUT, OPTIONS',\n            'Access-Control-Allow-Headers': 'Content-Type, Authorization'\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/custom-configs/[configId]/routing/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe-client.ts":
/*!**********************************!*\
  !*** ./src/lib/stripe-client.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TIER_CONFIGS: () => (/* binding */ TIER_CONFIGS),\n/* harmony export */   canPerformAction: () => (/* binding */ canPerformAction),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   getPriceIdForTier: () => (/* binding */ getPriceIdForTier),\n/* harmony export */   getTierConfig: () => (/* binding */ getTierConfig),\n/* harmony export */   getTierFromPriceId: () => (/* binding */ getTierFromPriceId),\n/* harmony export */   hasFeatureAccess: () => (/* binding */ hasFeatureAccess)\n/* harmony export */ });\n/* harmony import */ var _stripe_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./stripe-config */ \"(rsc)/./src/lib/stripe-config.ts\");\n// Client-safe Stripe utilities (no server-side Stripe instance)\n\nconst TIER_CONFIGS = {\n    free: {\n        name: 'Free',\n        price: '$0',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.FREE,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.FREE,\n        features: [\n            'Unlimited API requests',\n            '1 Custom Configuration',\n            '3 API Keys per config',\n            'All 300+ AI models',\n            'Strict fallback routing only',\n            'Basic analytics only',\n            'No custom roles, basic router only',\n            'Limited logs',\n            'Community support'\n        ],\n        limits: {\n            configurations: 1,\n            apiKeysPerConfig: 3,\n            apiRequests: 999999,\n            canUseAdvancedRouting: false,\n            canUseCustomRoles: false,\n            maxCustomRoles: 0,\n            canUsePromptEngineering: false,\n            canUseKnowledgeBase: false,\n            knowledgeBaseDocuments: 0,\n            canUseSemanticCaching: false\n        }\n    },\n    starter: {\n        name: 'Starter',\n        price: '$19',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.STARTER,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.STARTER,\n        features: [\n            'Unlimited API requests',\n            '4 Custom Configurations',\n            '5 API Keys per config',\n            'All 300+ AI models',\n            'Strict fallback + Complex routing (1 config limit)',\n            'Up to 3 custom roles',\n            'Intelligent role routing (1 config)',\n            'Prompt engineering (no file upload)',\n            'Enhanced logs and analytics',\n            'Community support'\n        ],\n        limits: {\n            configurations: 4,\n            apiKeysPerConfig: 5,\n            apiRequests: 999999,\n            canUseAdvancedRouting: true,\n            canUseCustomRoles: true,\n            maxCustomRoles: 3,\n            canUsePromptEngineering: true,\n            canUseKnowledgeBase: false,\n            knowledgeBaseDocuments: 0,\n            canUseSemanticCaching: false\n        }\n    },\n    professional: {\n        name: 'Professional',\n        price: '$49',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.PROFESSIONAL,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.PROFESSIONAL,\n        features: [\n            'Unlimited API requests',\n            '20 Custom Configurations',\n            '15 API Keys per config',\n            'All 300+ AI models',\n            'All advanced routing strategies',\n            'Unlimited custom roles',\n            'Prompt engineering + Knowledge base (5 documents)',\n            'Semantic caching',\n            'Advanced analytics and logging',\n            'Priority email support'\n        ],\n        limits: {\n            configurations: 20,\n            apiKeysPerConfig: 15,\n            apiRequests: 999999,\n            canUseAdvancedRouting: true,\n            canUseCustomRoles: true,\n            maxCustomRoles: 999999,\n            canUsePromptEngineering: true,\n            canUseKnowledgeBase: true,\n            knowledgeBaseDocuments: 5,\n            canUseSemanticCaching: true\n        }\n    },\n    enterprise: {\n        name: 'Enterprise',\n        price: '$149',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.ENTERPRISE,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.ENTERPRISE,\n        features: [\n            'Unlimited API requests',\n            'Unlimited configurations',\n            'Unlimited API keys',\n            'All 300+ models + priority access',\n            'All routing strategies',\n            'Unlimited custom roles',\n            'All features + priority support',\n            'Unlimited knowledge base documents',\n            'Advanced semantic caching',\n            'Custom integrations',\n            'Dedicated support + phone',\n            'SLA guarantee'\n        ],\n        limits: {\n            configurations: 999999,\n            apiKeysPerConfig: 999999,\n            apiRequests: 999999,\n            canUseAdvancedRouting: true,\n            canUseCustomRoles: true,\n            maxCustomRoles: 999999,\n            canUsePromptEngineering: true,\n            canUseKnowledgeBase: true,\n            knowledgeBaseDocuments: 999999,\n            canUseSemanticCaching: true\n        }\n    }\n};\nfunction getTierConfig(tier) {\n    return TIER_CONFIGS[tier];\n}\nfunction getPriceIdForTier(tier) {\n    return TIER_CONFIGS[tier].priceId;\n}\nfunction getTierFromPriceId(priceId) {\n    for (const [tier, config] of Object.entries(TIER_CONFIGS)){\n        if (config.priceId === priceId) {\n            return tier;\n        }\n    }\n    return 'free'; // Default fallback to free tier\n}\nfunction formatPrice(tier) {\n    return TIER_CONFIGS[tier].price;\n}\nfunction canPerformAction(tier, action, currentCount) {\n    const limits = TIER_CONFIGS[tier].limits;\n    switch(action){\n        case 'create_config':\n            return currentCount < limits.configurations;\n        case 'create_api_key':\n            return currentCount < limits.apiKeysPerConfig;\n        default:\n            return true;\n    }\n}\nfunction hasFeatureAccess(tier, feature) {\n    const limits = TIER_CONFIGS[tier].limits;\n    switch(feature){\n        case 'custom_roles':\n            return limits.canUseCustomRoles;\n        case 'knowledge_base':\n            return limits.canUseKnowledgeBase;\n        case 'advanced_routing':\n            return limits.canUseAdvancedRouting;\n        case 'prompt_engineering':\n            return limits.canUsePromptEngineering;\n        case 'semantic_caching':\n            return limits.canUseSemanticCaching;\n        case 'configurations':\n            return limits.configurations > 0; // All tiers can create at least some configurations\n        default:\n            return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe-client.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe-config.ts":
/*!**********************************!*\
  !*** ./src/lib/stripe-config.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PUBLIC_STRIPE_PRICE_IDS: () => (/* binding */ PUBLIC_STRIPE_PRICE_IDS),\n/* harmony export */   STRIPE_ENV_INFO: () => (/* binding */ STRIPE_ENV_INFO),\n/* harmony export */   STRIPE_KEYS: () => (/* binding */ STRIPE_KEYS),\n/* harmony export */   STRIPE_PRICE_IDS: () => (/* binding */ STRIPE_PRICE_IDS),\n/* harmony export */   STRIPE_PRODUCT_IDS: () => (/* binding */ STRIPE_PRODUCT_IDS)\n/* harmony export */ });\n// Stripe Configuration with Environment Detection\n// Automatically switches between test and live keys based on environment\nconst isProduction = \"development\" === 'production';\n// Stripe Keys - Auto-selected based on environment\nconst STRIPE_KEYS = {\n    publishableKey: isProduction ? process.env.STRIPE_LIVE_PUBLISHABLE_KEY : process.env.STRIPE_TEST_PUBLISHABLE_KEY,\n    secretKey: isProduction ? process.env.STRIPE_LIVE_SECRET_KEY : process.env.STRIPE_TEST_SECRET_KEY,\n    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET\n};\n// Stripe Price IDs - Auto-selected based on environment\nconst STRIPE_PRICE_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRICE_ID : process.env.STRIPE_TEST_FREE_PRICE_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRICE_ID : process.env.STRIPE_TEST_STARTER_PRICE_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID\n};\n// Stripe Product IDs - Auto-selected based on environment\nconst STRIPE_PRODUCT_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRODUCT_ID : process.env.STRIPE_TEST_FREE_PRODUCT_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRODUCT_ID : process.env.STRIPE_TEST_STARTER_PRODUCT_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRODUCT_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID : process.env.STRIPE_TEST_ENTERPRISE_PRODUCT_ID\n};\n// Public Price IDs for frontend (auto-selected based on environment)\nconst PUBLIC_STRIPE_PRICE_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRICE_ID : process.env.STRIPE_TEST_FREE_PRICE_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRICE_ID : process.env.STRIPE_TEST_STARTER_PRICE_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID\n};\n// Environment info for debugging\nconst STRIPE_ENV_INFO = {\n    isProduction,\n    environment: isProduction ? 'LIVE' : 'TEST',\n    keysUsed: {\n        publishable: STRIPE_KEYS.publishableKey ? STRIPE_KEYS.publishableKey.substring(0, 20) + '...' : 'undefined',\n        secret: STRIPE_KEYS.secretKey ? STRIPE_KEYS.secretKey.substring(0, 20) + '...' : 'undefined'\n    }\n};\n// Log environment info in development\nif (!isProduction) {\n    console.log('🔧 Stripe Environment:', STRIPE_ENV_INFO.environment);\n    console.log('🔑 Using keys:', STRIPE_ENV_INFO.keysUsed);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe-config.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcustom-configs%2F%5BconfigId%5D%2Frouting%2Froute&page=%2Fapi%2Fcustom-configs%2F%5BconfigId%5D%2Frouting%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustom-configs%2F%5BconfigId%5D%2Frouting%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();