'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  UserIcon,
  KeyIcon,
  ShieldCheckIcon,
  BellIcon,
  EyeIcon,
  EyeSlashIcon,
  TrashIcon,
  EnvelopeIcon
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';
import { useSubscription } from '@/hooks/useSubscription';

export default function SettingsPage() {
  const router = useRouter();
  const { user } = useSubscription();
  const supabase = createSupabaseBrowserClient();

  const [activeSection, setActiveSection] = useState('account');
  const [loading, setLoading] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showResetModal, setShowResetModal] = useState(false);
  const [isResetLoading, setIsResetLoading] = useState(false);
  const [showChangeEmailModal, setShowChangeEmailModal] = useState(false);
  const [isEmailLoading, setIsEmailLoading] = useState(false);
  const [emailChangeData, setEmailChangeData] = useState({
    newEmail: '',
    currentPassword: ''
  });

  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    securityAlerts: true,
    usageAlerts: true,
    marketingEmails: false
  });

  const [userStats, setUserStats] = useState({
    configCount: 0,
    apiKeyCount: 0,
    userApiKeyCount: 0
  });

  const sidebarItems = [
    { id: 'account', label: 'Account settings', icon: UserIcon },
    { id: 'notifications', label: 'Notifications', icon: BellIcon },
    { id: 'security', label: 'Security', icon: ShieldCheckIcon },
    { id: 'danger', label: 'Danger zone', icon: TrashIcon }
  ];

  // Load user data and stats
  useEffect(() => {
    if (user) {
      loadUserStats();
      loadNotificationSettings();
    }
  }, [user]);

  const loadUserStats = async () => {
    try {
      // Get configuration count
      const configResponse = await fetch('/api/custom-configs');
      const configData = configResponse.ok ? await configResponse.json() : [];
      const configs = Array.isArray(configData) ? configData : [];

      // Get user API keys count
      const userKeysResponse = await fetch('/api/user-api-keys');
      const userKeysData = userKeysResponse.ok ? await userKeysResponse.json() : { api_keys: [] };
      const userKeys = userKeysData.api_keys || [];

      // Get total API keys count across all configs
      let totalApiKeys = 0;
      for (const config of configs) {
        try {
          const keysResponse = await fetch(`/api/custom-configs/${config.id}/keys`);
          const keysData = keysResponse.ok ? await keysResponse.json() : { api_keys: [] };
          totalApiKeys += (keysData.api_keys || []).length;
        } catch (error) {
          console.error('Error loading keys for config:', config.id, error);
        }
      }

      setUserStats({
        configCount: configs.length,
        apiKeyCount: totalApiKeys,
        userApiKeyCount: userKeys.length
      });
    } catch (error) {
      console.error('Error loading user stats:', error);
    }
  };

  const loadNotificationSettings = async () => {
    try {
      // Load from user metadata or local storage
      const savedSettings = localStorage.getItem('notification_settings');
      if (savedSettings) {
        setNotificationSettings(JSON.parse(savedSettings));
      }
    } catch (error) {
      console.error('Error loading notification settings:', error);
    }
  };

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!passwordData.currentPassword.trim()) {
      toast.error('Current password is required');
      return;
    }

    if (!passwordData.newPassword.trim()) {
      toast.error('New password is required');
      return;
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error('New passwords do not match');
      return;
    }

    if (passwordData.newPassword.length < 8) {
      toast.error('Password must be at least 8 characters long');
      return;
    }

    if (passwordData.newPassword === passwordData.currentPassword) {
      toast.error('New password must be different from current password');
      return;
    }

    setLoading(true);

    try {
      // First verify current password by attempting to sign in
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: user?.email || '',
        password: passwordData.currentPassword
      });

      if (signInError) {
        toast.error('Current password is incorrect');
        setLoading(false);
        return;
      }

      // If current password is correct, update to new password
      const { error: updateError } = await supabase.auth.updateUser({
        password: passwordData.newPassword
      });

      if (updateError) throw updateError;

      toast.success('Password updated successfully');
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
    } catch (error: any) {
      console.error('Password update error:', error);
      toast.error(error.message || 'Failed to update password');
    } finally {
      setLoading(false);
    }
  };

  const handleNotificationUpdate = async () => {
    setLoading(true);

    try {
      // Save to local storage and user metadata
      localStorage.setItem('notification_settings', JSON.stringify(notificationSettings));

      // Also save to user metadata for persistence across devices
      const { error } = await supabase.auth.updateUser({
        data: {
          notification_preferences: notificationSettings
        }
      });

      if (error) throw error;

      toast.success('Notification preferences saved successfully');
    } catch (error: any) {
      console.error('Notification update error:', error);
      toast.error('Failed to save notification preferences');
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordReset = async () => {
    if (!user?.email) {
      toast.error('No email address found');
      return;
    }

    setIsResetLoading(true);
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(user.email, {
        redirectTo: `${window.location.origin}/auth/reset-password`
      });

      if (error) throw error;

      toast.success('Password reset email sent!', 'Check your inbox for instructions to reset your password.');
      setShowResetModal(false);
    } catch (error: any) {
      console.error('Password reset error:', error);
      toast.error('Failed to send reset email', error.message || 'Please try again.');
    } finally {
      setIsResetLoading(false);
    }
  };

  const handleEmailChange = async () => {
    if (!emailChangeData.newEmail.trim()) {
      toast.error('Please enter a new email address');
      return;
    }

    if (!emailChangeData.currentPassword.trim()) {
      toast.error('Current password is required to change email');
      return;
    }

    if (emailChangeData.newEmail === user?.email) {
      toast.error('New email must be different from current email');
      return;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(emailChangeData.newEmail)) {
      toast.error('Please enter a valid email address');
      return;
    }

    setIsEmailLoading(true);
    try {
      // First verify current password
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: user?.email || '',
        password: emailChangeData.currentPassword
      });

      if (signInError) {
        toast.error('Current password is incorrect');
        setIsEmailLoading(false);
        return;
      }

      // Update email
      const { error: updateError } = await supabase.auth.updateUser({
        email: emailChangeData.newEmail
      });

      if (updateError) throw updateError;

      toast.success('Email change initiated!', 'Check both your old and new email addresses for confirmation instructions.');
      setShowChangeEmailModal(false);
      setEmailChangeData({ newEmail: '', currentPassword: '' });
    } catch (error: any) {
      console.error('Email change error:', error);
      toast.error('Failed to change email', error.message || 'Please try again.');
    } finally {
      setIsEmailLoading(false);
    }
  };

  const handleAccountDeletion = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // Call our account deletion API
      const response = await fetch('/api/user/delete-account', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete account');
      }

      toast.success('Account deleted successfully. You will be redirected to the homepage.');

      // Sign out and redirect
      await supabase.auth.signOut();
      setTimeout(() => {
        window.location.href = '/';
      }, 2000);

    } catch (error: any) {
      console.error('Account deletion error:', error);
      toast.error(error.message || 'Failed to delete account. Please contact support.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex-1 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600 mt-1">Manage your account settings and preferences</p>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {sidebarItems.map((item) => {
            const Icon = item.icon;
            return (
              <button
                key={item.id}
                onClick={() => setActiveSection(item.id)}
                className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeSection === item.id
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4" />
                {item.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Content Area */}
      <div className="max-w-4xl">
            {activeSection === 'account' && (
              <div className="space-y-8">
                {/* Email Address Section */}
                <div className="bg-white rounded-lg border border-gray-200 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Email address</h3>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-600">Your email address is <span className="font-medium text-gray-900">{user?.email}</span></p>
                      <p className="text-sm text-gray-500 mt-1">This is used for login and important notifications</p>
                    </div>
                    <button
                      className="px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-md transition-colors"
                      onClick={() => setShowChangeEmailModal(true)}
                    >
                      Change
                    </button>
                  </div>
                </div>

                {/* Account Information */}
                <div className="bg-white rounded-lg border border-gray-200 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-6">Account Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-1">Full Name</h4>
                        <p className="text-gray-600">
                          {user?.user_metadata?.full_name || user?.user_metadata?.first_name + ' ' + user?.user_metadata?.last_name || 'Not set'}
                        </p>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-1">Account Created</h4>
                        <p className="text-gray-600">
                          {user?.created_at ? new Date(user.created_at).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          }) : 'Unknown'}
                        </p>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-1">Account Usage</h4>
                        <div className="space-y-1 text-sm text-gray-600">
                          <p>{userStats.configCount} configurations</p>
                          <p>{userStats.apiKeyCount} API keys</p>
                          <p>{userStats.userApiKeyCount} user-generated keys</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Password Section */}
                <div className="bg-white rounded-lg border border-gray-200 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-6">Password</h3>
                  <form onSubmit={handlePasswordChange} className="space-y-6">
                    <div>
                      <Label htmlFor="currentPassword" className="text-sm font-medium text-gray-700">Current password</Label>
                      <div className="relative mt-2">
                        <Input
                          id="currentPassword"
                          type={showCurrentPassword ? "text" : "password"}
                          value={passwordData.currentPassword}
                          onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}
                          placeholder="••••••••••"
                          className="pr-10"
                          required
                        />
                        <button
                          type="button"
                          onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                        >
                          {showCurrentPassword ? <EyeSlashIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
                        </button>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="newPassword" className="text-sm font-medium text-gray-700">New password</Label>
                        <div className="relative mt-2">
                          <Input
                            id="newPassword"
                            type={showNewPassword ? "text" : "password"}
                            value={passwordData.newPassword}
                            onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                            placeholder="••••••••••"
                            className="pr-10"
                            required
                          />
                          <button
                            type="button"
                            onClick={() => setShowNewPassword(!showNewPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                          >
                            {showNewPassword ? <EyeSlashIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
                          </button>
                        </div>
                      </div>
                      <div>
                        <Label htmlFor="confirmPassword" className="text-sm font-medium text-gray-700">Confirm new password</Label>
                        <div className="relative mt-2">
                          <Input
                            id="confirmPassword"
                            type={showConfirmPassword ? "text" : "password"}
                            value={passwordData.confirmPassword}
                            onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                            placeholder="••••••••••"
                            className="pr-10"
                            required
                          />
                          <button
                            type="button"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                          >
                            {showConfirmPassword ? <EyeSlashIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
                          </button>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-gray-600">
                        Can't remember your current password? <button
                          type="button"
                          className="text-blue-600 hover:text-blue-700 font-medium"
                          onClick={() => setShowResetModal(true)}
                        >
                          Reset your password
                        </button>
                      </div>
                      <Button type="submit" disabled={loading} className="bg-orange-600 hover:bg-orange-700">
                        {loading ? 'Saving...' : 'Save password'}
                      </Button>
                    </div>
                  </form>
                </div>
              </div>
            )}

            {activeSection === 'notifications' && (
              <div className="space-y-8">
                <div className="bg-white rounded-lg border border-gray-200 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-6">Notification Preferences</h3>
                  <div className="space-y-6">
                    {Object.entries(notificationSettings).map(([key, value]) => (
                      <div key={key} className="flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0">
                        <div>
                          <h4 className="text-sm font-medium text-gray-900">
                            {key === 'emailNotifications' && 'Email Notifications'}
                            {key === 'securityAlerts' && 'Security Alerts'}
                            {key === 'usageAlerts' && 'Usage Alerts'}
                            {key === 'marketingEmails' && 'Marketing Emails'}
                          </h4>
                          <p className="text-sm text-gray-500 mt-1">
                            {key === 'emailNotifications' && 'Receive general email notifications'}
                            {key === 'securityAlerts' && 'Get notified about security events'}
                            {key === 'usageAlerts' && 'Alerts about API usage and limits'}
                            {key === 'marketingEmails' && 'Product updates and promotional content'}
                          </p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={value}
                            onChange={(e) => setNotificationSettings(prev => ({ ...prev, [key]: e.target.checked }))}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-500"></div>
                        </label>
                      </div>
                    ))}
                    <div className="pt-4">
                      <Button onClick={handleNotificationUpdate} disabled={loading} className="bg-orange-600 hover:bg-orange-700">
                        {loading ? 'Saving...' : 'Save preferences'}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeSection === 'security' && (
              <div className="space-y-8">
                <div className="bg-white rounded-lg border border-gray-200 p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-lg font-semibold text-gray-900">Security Overview</h3>
                    <div className="flex items-center gap-2 px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                      <ShieldCheckIcon className="h-4 w-4" />
                      Secure
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Password Security</h4>
                        <p className="text-sm text-gray-600 mb-3">Change your password regularly for better security</p>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setActiveSection('account')}
                          className="text-orange-600 border-orange-300 hover:bg-orange-50"
                        >
                          Change Password
                        </Button>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-1">Account Activity</h4>
                        <p className="text-sm text-gray-600">
                          {userStats.configCount + userStats.apiKeyCount + userStats.userApiKeyCount} total resources created
                        </p>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-1">Account Created</h4>
                        <p className="text-sm text-gray-600">
                          {user?.created_at ? new Date(user.created_at).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          }) : 'Unknown'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeSection === 'danger' && (
              <div className="space-y-8">
                <div className="bg-white rounded-lg border border-red-200 p-6">
                  <div className="flex items-center gap-3 mb-6">
                    <TrashIcon className="h-6 w-6 text-red-500" />
                    <h3 className="text-lg font-semibold text-red-900">Danger Zone</h3>
                  </div>

                  <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                    <h4 className="text-lg font-medium text-red-900 mb-3">Delete Account</h4>
                    <p className="text-sm text-red-700 mb-4">
                      Permanently delete your RouKey account and all associated data. This action cannot be undone.
                    </p>
                    <div className="bg-white border border-red-200 rounded-md p-4 mb-6">
                      <h5 className="text-sm font-medium text-red-900 mb-2">This will permanently delete:</h5>
                      <ul className="text-sm text-red-700 space-y-1">
                        <li>• {userStats.configCount} API configurations</li>
                        <li>• {userStats.apiKeyCount} API keys</li>
                        <li>• {userStats.userApiKeyCount} user-generated API keys</li>
                        <li>• All usage logs and analytics</li>
                        <li>• Your subscription and billing information</li>
                      </ul>
                    </div>
                    <Button
                      variant="outline"
                      className="text-red-600 border-red-300 hover:bg-red-100 hover:border-red-400"
                      onClick={() => setShowDeleteModal(true)}
                      disabled={loading}
                    >
                      Delete my account
                    </Button>
                  </div>
                </div>
              </div>
            )}
      </div>

      {/* Change Email Modal */}
      {showChangeEmailModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-2xl max-w-md w-full p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                <EnvelopeIcon className="h-5 w-5 text-orange-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Change Email Address</h3>
            </div>

            <p className="text-gray-600 mb-6">
              Enter your new email address and current password to change your email.
              You'll need to verify both your old and new email addresses.
            </p>

            <form onSubmit={(e) => { e.preventDefault(); handleEmailChange(); }} className="space-y-4">
              <div>
                <label htmlFor="newEmail" className="block text-sm font-medium text-gray-700 mb-2">
                  New Email Address
                </label>
                <input
                  id="newEmail"
                  type="email"
                  value={emailChangeData.newEmail}
                  onChange={(e) => setEmailChangeData(prev => ({ ...prev, newEmail: e.target.value }))}
                  placeholder="Enter your new email address"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200"
                  required
                />
              </div>

              <div>
                <label htmlFor="currentPasswordEmail" className="block text-sm font-medium text-gray-700 mb-2">
                  Current Password
                </label>
                <input
                  id="currentPasswordEmail"
                  type="password"
                  value={emailChangeData.currentPassword}
                  onChange={(e) => setEmailChangeData(prev => ({ ...prev, currentPassword: e.target.value }))}
                  placeholder="Enter your current password"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200"
                  required
                />
              </div>

              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mt-4">
                <h4 className="text-sm font-medium text-orange-900 mb-2">What happens next:</h4>
                <ul className="text-sm text-orange-700 space-y-1">
                  <li>• You'll receive confirmation emails at both addresses</li>
                  <li>• Click the links in both emails to confirm the change</li>
                  <li>• Your email will be updated once both are verified</li>
                  <li>• You can continue using your current email until then</li>
                </ul>
              </div>

              <div className="flex gap-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setShowChangeEmailModal(false);
                    setEmailChangeData({ newEmail: '', currentPassword: '' });
                  }}
                  className="flex-1"
                  disabled={isEmailLoading}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isEmailLoading}
                  className="flex-1 bg-orange-600 hover:bg-orange-700 text-white"
                >
                  {isEmailLoading ? (
                    <div className="flex items-center justify-center">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      Changing...
                    </div>
                  ) : (
                    'Change Email'
                  )}
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Password Reset Confirmation Modal */}
      {showResetModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-2xl max-w-md w-full p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <EnvelopeIcon className="h-5 w-5 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Reset Your Password</h3>
            </div>

            <p className="text-gray-600 mb-4">
              We'll send a password reset link to <span className="font-medium">{user?.email}</span>.
              You can use this link to create a new password.
            </p>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <h4 className="text-sm font-medium text-blue-900 mb-2">What happens next:</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• You'll receive an email with a secure reset link</li>
                <li>• Click the link to create a new password</li>
                <li>• Your current password will remain active until you complete the reset</li>
              </ul>
            </div>

            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => setShowResetModal(false)}
                className="flex-1"
                disabled={isResetLoading}
              >
                Cancel
              </Button>
              <Button
                onClick={handlePasswordReset}
                disabled={isResetLoading}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
              >
                {isResetLoading ? (
                  <div className="flex items-center justify-center">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Sending...
                  </div>
                ) : (
                  'Send Reset Link'
                )}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Account Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-2xl max-w-md w-full p-6">
            <div className="flex items-center gap-3 mb-4">
              <TrashIcon className="h-6 w-6 text-red-500" />
              <h3 className="text-lg font-semibold text-gray-900">Delete Account</h3>
            </div>

            <p className="text-gray-600 mb-4">
              Are you absolutely sure you want to delete your account? This action cannot be undone.
            </p>

            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <h4 className="text-sm font-medium text-red-900 mb-2">This will permanently delete:</h4>
              <ul className="text-sm text-red-700 space-y-1">
                <li>• {userStats.configCount} API configurations</li>
                <li>• {userStats.apiKeyCount} API keys</li>
                <li>• {userStats.userApiKeyCount} user-generated API keys</li>
                <li>• All usage logs and analytics</li>
                <li>• Your subscription and billing information</li>
              </ul>
            </div>

            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => setShowDeleteModal(false)}
                className="flex-1"
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                onClick={handleAccountDeletion}
                disabled={loading}
                className="flex-1 bg-red-600 hover:bg-red-700 text-white"
              >
                {loading ? 'Deleting...' : 'Delete Account'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
