'use client';

import { useState, Suspense, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { EyeIcon, EyeSlashIcon, XMarkIcon, EnvelopeIcon } from '@heroicons/react/24/outline';
import EnhancedGridBackground from '@/components/landing/EnhancedGridBackground';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';
import { useRouter, useSearchParams } from 'next/navigation';
import { useToast } from '@/components/ui/Toast';

function SignInPageContent() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Password reset modal state
  const [showResetModal, setShowResetModal] = useState(false);
  const [resetEmail, setResetEmail] = useState('');
  const [isResetLoading, setIsResetLoading] = useState(false);
  const [resetError, setResetError] = useState('');

  const router = useRouter();
  const searchParams = useSearchParams();
  const supabase = createSupabaseBrowserClient();
  const { success, error: toastError } = useToast();

  useEffect(() => {
    // TEMPORARILY DISABLED: Check if user is already signed in
    // This is causing redirect loops, so we'll let users manually fill out the form
    console.log('Signin page loaded, automatic session check disabled to prevent redirect loops');

    /*
    const checkUser = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      if (session) {
        const redirectTo = searchParams.get('redirectTo');
        const plan = searchParams.get('plan');

        if (plan && ['starter', 'professional', 'enterprise'].includes(plan)) {
          router.push(`/pricing?plan=${plan}&checkout=true`);
        } else if (redirectTo) {
          router.push(redirectTo);
        } else {
          router.push('/dashboard');
        }
      }
    };
    checkUser();
    */
  }, [router, searchParams, supabase.auth]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw error;
      }

      if (data.user) {
        console.log('Sign in successful, user:', data.user.id);
        console.log('Current URL:', window.location.href);
        console.log('Environment:', process.env.NODE_ENV);
        console.log('Site URL:', process.env.NEXT_PUBLIC_SITE_URL);

        // Wait a moment for session to be established
        await new Promise(resolve => setTimeout(resolve, 500));

        // Handle redirect logic - users should be able to access dashboard regardless of subscription
        const redirectTo = searchParams.get('redirectTo');
        const plan = searchParams.get('plan');
        const email = searchParams.get('email');
        const checkoutUserId = searchParams.get('checkout_user_id');

        console.log('Redirect params:', { redirectTo, plan, email, checkoutUserId });

        // If this is specifically a checkout flow, redirect to checkout
        if (checkoutUserId && plan && ['starter', 'professional', 'enterprise'].includes(plan)) {
          const checkoutUrl = `/checkout?plan=${plan}&user_id=${data.user.id}${email ? `&email=${encodeURIComponent(email)}` : ''}`;
          console.log('Redirecting to checkout:', checkoutUrl);
          router.push(checkoutUrl);
        } else if (plan && ['starter', 'professional', 'enterprise'].includes(plan)) {
          // If there's a plan parameter, redirect to pricing for upgrade
          const checkoutUrl = `/checkout?plan=${plan}&user_id=${data.user.id}${email ? `&email=${encodeURIComponent(email)}` : ''}`;
          console.log('Redirecting to checkout for plan:', checkoutUrl);
          router.push(checkoutUrl);
        } else if (redirectTo) {
          // Redirect to specified location
          console.log('Redirecting to specified location:', redirectTo);
          router.push(redirectTo);
        } else {
          // Default redirect to dashboard - all users should be able to access dashboard
          console.log('Redirecting to dashboard');
          router.push('/dashboard');
        }
      }
    } catch (err: any) {
      console.error('Sign in error:', err);
      setError(err.message || 'Invalid email or password. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordReset = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsResetLoading(true);
    setResetError('');

    if (!resetEmail.trim()) {
      setResetError('Please enter your email address');
      setIsResetLoading(false);
      return;
    }

    try {
      const { error } = await supabase.auth.resetPasswordForEmail(resetEmail, {
        redirectTo: `${window.location.origin}/auth/reset-password`
      });

      if (error) {
        throw error;
      }

      success('Password reset email sent!', 'Check your inbox for instructions to reset your password.');
      setShowResetModal(false);
      setResetEmail('');
    } catch (err: any) {
      console.error('Password reset error:', err);
      setResetError(err.message || 'Failed to send reset email. Please try again.');
      toastError('Failed to send reset email', err.message || 'Please try again.');
    } finally {
      setIsResetLoading(false);
    }
  };

  const openResetModal = () => {
    setResetEmail(email); // Pre-fill with current email if available
    setResetError('');
    setShowResetModal(true);
  };



  return (
    <div className="min-h-screen bg-white relative overflow-hidden flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      {/* Grid Background */}
      <EnhancedGridBackground
        gridSize={50}
        opacity={0.064}
        color="#000000"
        variant="subtle"
        animated={true}
        className="fixed inset-0"
      />

      {/* Orange Accent Grid */}
      <div className="absolute inset-0">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `radial-gradient(circle, rgba(255, 107, 53, 0.12) 1px, transparent 1px)`,
            backgroundSize: '100px 100px',
            mask: `radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)`,
            WebkitMask: `radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)`
          }}
        ></div>
      </div>

      <div className="relative z-10 w-full max-w-6xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Side - Branding */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="hidden lg:block"
          >
            <div className="bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-3xl p-12 text-white relative overflow-hidden">
              {/* Background Pattern */}
              <div
                className="absolute inset-0 opacity-20"
                style={{
                  backgroundImage: `
                    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)
                  `,
                  backgroundSize: '30px 30px'
                }}
              ></div>

              <div className="relative z-10">
                <div className="mb-8">
                  <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mb-6 backdrop-blur-sm">
                    <Image
                      src="/roukey_logo.png"
                      alt="RouKey"
                      width={48}
                      height={48}
                      className="w-12 h-12 object-contain"
                      priority
                    />
                  </div>
                  <h1 className="text-4xl font-bold mb-4">Welcome to RouKey</h1>
                  <p className="text-xl text-white/90 mb-8">
                    Access to <span className="font-bold">UNLIMITED</span> AI requests across 300+ models
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                    <span className="text-white/90">Intelligent Role Routing</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                    <span className="text-white/90">Enterprise Security</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                    <span className="text-white/90">No Request Limits</span>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Right Side - Form */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="w-full max-w-md mx-auto lg:mx-0"
          >
            {/* Header */}
            <div className="text-center mb-8">
              <Link href="/" className="inline-flex items-center space-x-3 mb-8 justify-center lg:justify-start">
                <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-lg border border-gray-100 p-1">
                  <Image
                    src="/roukey_logo.png"
                    alt="RouKey"
                    width={40}
                    height={40}
                    className="w-full h-full object-contain"
                    priority
                  />
                </div>
                <span className="text-3xl font-bold text-black">RouKey</span>
              </Link>

              <h2 className="text-4xl font-bold text-black mb-3">Sign In</h2>
              <p className="text-gray-600 text-lg">Welcome back to your AI gateway</p>
            </div>

            {/* Enhanced Form */}
            <div className="bg-white rounded-3xl shadow-xl border border-gray-100 p-8 backdrop-blur-sm relative overflow-hidden">
              {/* Form Background Pattern */}
              <div
                className="absolute inset-0 opacity-5"
                style={{
                  backgroundImage: `
                    linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)
                  `,
                  backgroundSize: '20px 20px'
                }}
              ></div>
              <div className="relative z-10">
                {/* Success message for account creation */}
                {searchParams.get('message') === 'account_created' && (
                  <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-xl">
                    <p className="text-green-600 text-sm font-medium">
                      ✅ Account created successfully! Please sign in to complete your checkout.
                    </p>
                  </div>
                )}

                {error && (
                  <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
                    <p className="text-red-600 text-sm">{error}</p>
                  </div>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <label htmlFor="email" className="block text-sm font-semibold text-gray-800 mb-3">
                      📧 Email Address
                    </label>
                    <input
                      id="email"
                      name="email"
                      type="email"
                      autoComplete="email"
                      required
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white"
                      placeholder="Enter your email"
                    />
                  </div>

                  <div>
                    <label htmlFor="password" className="block text-sm font-semibold text-gray-800 mb-3">
                      🔒 Password
                    </label>
                    <div className="relative">
                      <input
                        id="password"
                        name="password"
                        type={showPassword ? 'text' : 'password'}
                        autoComplete="current-password"
                        required
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="w-full px-5 py-4 pr-14 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white"
                        placeholder="Enter your password"
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-[#ff6b35] transition-colors"
                      >
                        {showPassword ? (
                          <EyeSlashIcon className="h-5 w-5" />
                        ) : (
                          <EyeIcon className="h-5 w-5" />
                        )}
                      </button>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <input
                        id="remember-me"
                        name="remember-me"
                        type="checkbox"
                        className="h-5 w-5 text-[#ff6b35] focus:ring-[#ff6b35] border-gray-300 rounded-lg"
                      />
                      <label htmlFor="remember-me" className="ml-3 block text-sm font-medium text-gray-700">
                        Remember me
                      </label>
                    </div>

                    <button
                      type="button"
                      onClick={openResetModal}
                      className="text-sm text-[#ff6b35] hover:text-[#e55a2b] font-semibold transition-colors"
                    >
                      Forgot password?
                    </button>
                  </div>

                  <button
                    type="submit"
                    disabled={isLoading}
                    className="w-full bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white py-4 px-6 rounded-xl font-bold text-lg hover:from-[#e55a2b] hover:to-[#e8851a] focus:ring-4 focus:ring-[#ff6b35]/30 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02]"
                  >
                    {isLoading ? (
                      <div className="flex items-center justify-center">
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                        Signing in...
                      </div>
                    ) : (
                      'Sign In'
                    )}
                  </button>
                </form>




              </div>
            </div>

            {/* Sign Up Link */}
            <div className="text-center mt-8">
              <p className="text-gray-600 text-lg">
                Don't have an account?{' '}
                <Link href="/auth/signup" className="text-[#ff6b35] hover:text-[#e55a2b] font-bold transition-colors">
                  Sign up for free
                </Link>
              </p>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Password Reset Modal */}
      {showResetModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            {/* Background overlay */}
            <div
              className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
              onClick={() => setShowResetModal(false)}
            ></div>

            {/* Modal */}
            <div className="inline-block align-bottom bg-white rounded-2xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-6 pt-6 pb-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-bold text-gray-900">Reset Your Password</h3>
                  <button
                    onClick={() => setShowResetModal(false)}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                <p className="text-gray-600 mb-6">
                  Enter your email address and we'll send you a link to reset your password.
                </p>

                {resetError && (
                  <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-red-600 text-sm">{resetError}</p>
                  </div>
                )}

                <form onSubmit={handlePasswordReset}>
                  <div className="mb-6">
                    <label htmlFor="resetEmail" className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address
                    </label>
                    <div className="relative">
                      <input
                        id="resetEmail"
                        type="email"
                        value={resetEmail}
                        onChange={(e) => setResetEmail(e.target.value)}
                        placeholder="Enter your email address"
                        className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent transition-all duration-200"
                        required
                      />
                      <EnvelopeIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                    </div>
                  </div>

                  <div className="flex space-x-3">
                    <button
                      type="button"
                      onClick={() => setShowResetModal(false)}
                      className="flex-1 px-4 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={isResetLoading}
                      className="flex-1 px-4 py-3 text-sm font-medium text-white bg-[#ff6b35] border border-transparent rounded-xl hover:bg-[#e55a2b] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#ff6b35] disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      {isResetLoading ? (
                        <div className="flex items-center justify-center">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                          Sending...
                        </div>
                      ) : (
                        'Send Reset Link'
                      )}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default function SignInPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    }>
      <SignInPageContent />
    </Suspense>
  );
}
