"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a6b18f1650dc\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImE2YjE4ZjE2NTBkY1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/LayoutContent.tsx":
/*!******************************************!*\
  !*** ./src/components/LayoutContent.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LayoutContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navbar */ \"(app-pages-browser)/./src/components/Navbar.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar.tsx\");\n/* harmony import */ var _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/SidebarContext */ \"(app-pages-browser)/./src/contexts/SidebarContext.tsx\");\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _components_OptimisticLoadingScreen__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/OptimisticLoadingScreen */ \"(app-pages-browser)/./src/components/OptimisticLoadingScreen.tsx\");\n/* harmony import */ var _components_OptimisticPageLoader__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/OptimisticPageLoader */ \"(app-pages-browser)/./src/components/OptimisticPageLoader.tsx\");\n/* harmony import */ var _hooks_useAdvancedPreloading__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useAdvancedPreloading */ \"(app-pages-browser)/./src/hooks/useAdvancedPreloading.ts\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction LayoutContentInner(param) {\n    let { children } = param;\n    _s();\n    const { isCollapsed, isHovered, collapseSidebar } = (0,_contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_4__.useSidebar)();\n    const navigationContext = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_5__.useNavigationSafe)();\n    const { isNavigating, targetRoute, isPageCached } = navigationContext || {\n        isNavigating: false,\n        targetRoute: null,\n        isPageCached: ()=>false\n    };\n    // Toast notifications\n    const { toasts, removeToast } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    // Track if we're on desktop (lg breakpoint and above)\n    const [isDesktop, setIsDesktop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LayoutContentInner.useEffect\": ()=>{\n            const checkIsDesktop = {\n                \"LayoutContentInner.useEffect.checkIsDesktop\": ()=>{\n                    setIsDesktop(window.innerWidth >= 1024); // lg breakpoint\n                }\n            }[\"LayoutContentInner.useEffect.checkIsDesktop\"];\n            // Check on mount\n            checkIsDesktop();\n            // Listen for resize events\n            window.addEventListener('resize', checkIsDesktop);\n            return ({\n                \"LayoutContentInner.useEffect\": ()=>window.removeEventListener('resize', checkIsDesktop)\n            })[\"LayoutContentInner.useEffect\"];\n        }\n    }[\"LayoutContentInner.useEffect\"], []);\n    // Calculate actual sidebar width based on collapsed and hover states\n    // Only apply sidebar width on desktop, mobile uses overlay\n    const sidebarWidth = isDesktop ? !isCollapsed || isHovered ? 256 : 64 : 0;\n    // Initialize advanced preloading system\n    (0,_hooks_useAdvancedPreloading__WEBPACK_IMPORTED_MODULE_8__.useAdvancedPreloading)({\n        maxConcurrent: 2,\n        idleTimeout: 1500,\n        backgroundDelay: 3000\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:block fixed left-0 top-0 h-full z-40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden fixed inset-0 z-50 \".concat(isCollapsed ? 'pointer-events-none' : ''),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onClick: collapseSidebar,\n                        className: \"absolute inset-0 bg-black transition-opacity duration-200 ease-out cursor-pointer \".concat(isCollapsed ? 'opacity-0' : 'opacity-50')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute left-0 top-0 h-full transform transition-transform duration-200 ease-out \".concat(isCollapsed ? '-translate-x-full' : 'translate-x-0'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden min-w-0 transition-all duration-200 ease-out\",\n                style: {\n                    marginLeft: \"\".concat(sidebarWidth, \"px\")\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed top-0 right-0 z-30 transition-all duration-200 ease-out\",\n                        style: {\n                            left: \"\".concat(sidebarWidth, \"px\")\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-y-auto content-area mt-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 sm:p-6 lg:p-8 w-full \".concat(// When sidebar is expanded, use standard max width with centering\n                            // When sidebar is collapsed, use larger max width or no max width\n                            isDesktop && (!isCollapsed || isHovered) ? 'max-w-7xl mx-auto' : isDesktop ? 'max-w-none px-8' : 'max-w-7xl mx-auto'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"page-transition\",\n                                children: isNavigating && targetRoute ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OptimisticPageLoader__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    targetRoute: targetRoute,\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 17\n                                }, this) : children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toast__WEBPACK_IMPORTED_MODULE_9__.ToastContainer, {\n                toasts: toasts,\n                onRemove: removeToast\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n_s(LayoutContentInner, \"rilvE/GaX21XzNPf5z7jhTgb3B0=\", false, function() {\n    return [\n        _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_4__.useSidebar,\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_5__.useNavigationSafe,\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_9__.useToast,\n        _hooks_useAdvancedPreloading__WEBPACK_IMPORTED_MODULE_8__.useAdvancedPreloading\n    ];\n});\n_c = LayoutContentInner;\nfunction LayoutContent(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OptimisticLoadingScreen__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            targetRoute: null\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n            lineNumber: 124,\n            columnNumber: 25\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LayoutContentInner, {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, this);\n}\n_c1 = LayoutContent;\nvar _c, _c1;\n$RefreshReg$(_c, \"LayoutContentInner\");\n$RefreshReg$(_c1, \"LayoutContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LayoutContent.tsx\n"));

/***/ })

});