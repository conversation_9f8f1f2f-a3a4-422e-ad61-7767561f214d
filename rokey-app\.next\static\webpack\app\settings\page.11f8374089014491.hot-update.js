"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/settings/page",{

/***/ "(app-pages-browser)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SettingsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction SettingsPage() {\n    var _user_user_metadata, _user_user_metadata1, _user_user_metadata2;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_8__.useSubscription)();\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_7__.createSupabaseBrowserClient)();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('account');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCurrentPassword, setShowCurrentPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNewPassword, setShowNewPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDeleteModal, setShowDeleteModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showResetModal, setShowResetModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isResetLoading, setIsResetLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showChangeEmailModal, setShowChangeEmailModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEmailLoading, setIsEmailLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [emailChangeData, setEmailChangeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        newEmail: '',\n        currentPassword: ''\n    });\n    const [passwordData, setPasswordData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n    });\n    const [notificationSettings, setNotificationSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        emailNotifications: true,\n        securityAlerts: true,\n        usageAlerts: true,\n        marketingEmails: false\n    });\n    const [userStats, setUserStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        configCount: 0,\n        apiKeyCount: 0,\n        userApiKeyCount: 0\n    });\n    const sidebarItems = [\n        {\n            id: 'account',\n            label: 'Account settings',\n            icon: _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            id: 'notifications',\n            label: 'Notifications',\n            icon: _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            id: 'security',\n            label: 'Security',\n            icon: _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        },\n        {\n            id: 'danger',\n            label: 'Danger zone',\n            icon: _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        }\n    ];\n    // Load user data and stats\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsPage.useEffect\": ()=>{\n            if (user) {\n                loadUserStats();\n                loadNotificationSettings();\n            }\n        }\n    }[\"SettingsPage.useEffect\"], [\n        user\n    ]);\n    const loadUserStats = async ()=>{\n        try {\n            // Get configuration count\n            const configResponse = await fetch('/api/custom-configs');\n            const configData = configResponse.ok ? await configResponse.json() : [];\n            const configs = Array.isArray(configData) ? configData : [];\n            // Get user API keys count\n            const userKeysResponse = await fetch('/api/user-api-keys');\n            const userKeysData = userKeysResponse.ok ? await userKeysResponse.json() : {\n                api_keys: []\n            };\n            const userKeys = userKeysData.api_keys || [];\n            // Get total API keys count across all configs\n            let totalApiKeys = 0;\n            for (const config of configs){\n                try {\n                    const keysResponse = await fetch(\"/api/custom-configs/\".concat(config.id, \"/keys\"));\n                    const keysData = keysResponse.ok ? await keysResponse.json() : {\n                        api_keys: []\n                    };\n                    totalApiKeys += (keysData.api_keys || []).length;\n                } catch (error) {\n                    console.error('Error loading keys for config:', config.id, error);\n                }\n            }\n            setUserStats({\n                configCount: configs.length,\n                apiKeyCount: totalApiKeys,\n                userApiKeyCount: userKeys.length\n            });\n        } catch (error) {\n            console.error('Error loading user stats:', error);\n        }\n    };\n    const loadNotificationSettings = async ()=>{\n        try {\n            // Load from user metadata or local storage\n            const savedSettings = localStorage.getItem('notification_settings');\n            if (savedSettings) {\n                setNotificationSettings(JSON.parse(savedSettings));\n            }\n        } catch (error) {\n            console.error('Error loading notification settings:', error);\n        }\n    };\n    const handlePasswordChange = async (e)=>{\n        e.preventDefault();\n        if (!passwordData.currentPassword.trim()) {\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('Current password is required');\n            return;\n        }\n        if (!passwordData.newPassword.trim()) {\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('New password is required');\n            return;\n        }\n        if (passwordData.newPassword !== passwordData.confirmPassword) {\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('New passwords do not match');\n            return;\n        }\n        if (passwordData.newPassword.length < 8) {\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('Password must be at least 8 characters long');\n            return;\n        }\n        if (passwordData.newPassword === passwordData.currentPassword) {\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('New password must be different from current password');\n            return;\n        }\n        setLoading(true);\n        try {\n            // First verify current password by attempting to sign in\n            const { error: signInError } = await supabase.auth.signInWithPassword({\n                email: (user === null || user === void 0 ? void 0 : user.email) || '',\n                password: passwordData.currentPassword\n            });\n            if (signInError) {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('Current password is incorrect');\n                setLoading(false);\n                return;\n            }\n            // If current password is correct, update to new password\n            const { error: updateError } = await supabase.auth.updateUser({\n                password: passwordData.newPassword\n            });\n            if (updateError) throw updateError;\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('Password updated successfully');\n            setPasswordData({\n                currentPassword: '',\n                newPassword: '',\n                confirmPassword: ''\n            });\n        } catch (error) {\n            console.error('Password update error:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(error.message || 'Failed to update password');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleNotificationUpdate = async ()=>{\n        setLoading(true);\n        try {\n            // Save to local storage and user metadata\n            localStorage.setItem('notification_settings', JSON.stringify(notificationSettings));\n            // Also save to user metadata for persistence across devices\n            const { error } = await supabase.auth.updateUser({\n                data: {\n                    notification_preferences: notificationSettings\n                }\n            });\n            if (error) throw error;\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('Notification preferences saved successfully');\n        } catch (error) {\n            console.error('Notification update error:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('Failed to save notification preferences');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handlePasswordReset = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) {\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('No email address found');\n            return;\n        }\n        setIsResetLoading(true);\n        try {\n            const { error } = await supabase.auth.resetPasswordForEmail(user.email, {\n                redirectTo: \"\".concat(window.location.origin, \"/auth/reset-password\")\n            });\n            if (error) throw error;\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('Password reset email sent!', 'Check your inbox for instructions to reset your password.');\n            setShowResetModal(false);\n        } catch (error) {\n            console.error('Password reset error:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('Failed to send reset email', error.message || 'Please try again.');\n        } finally{\n            setIsResetLoading(false);\n        }\n    };\n    const handleEmailChange = async ()=>{\n        if (!emailChangeData.newEmail.trim()) {\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('Please enter a new email address');\n            return;\n        }\n        if (!emailChangeData.currentPassword.trim()) {\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('Current password is required to change email');\n            return;\n        }\n        if (emailChangeData.newEmail === (user === null || user === void 0 ? void 0 : user.email)) {\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('New email must be different from current email');\n            return;\n        }\n        // Basic email validation\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(emailChangeData.newEmail)) {\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('Please enter a valid email address');\n            return;\n        }\n        setIsEmailLoading(true);\n        try {\n            // First verify current password\n            const { error: signInError } = await supabase.auth.signInWithPassword({\n                email: (user === null || user === void 0 ? void 0 : user.email) || '',\n                password: emailChangeData.currentPassword\n            });\n            if (signInError) {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('Current password is incorrect');\n                setIsEmailLoading(false);\n                return;\n            }\n            // Update email\n            const { error: updateError } = await supabase.auth.updateUser({\n                email: emailChangeData.newEmail\n            });\n            if (updateError) throw updateError;\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('Email change initiated!', 'Check both your old and new email addresses for confirmation instructions.');\n            setShowChangeEmailModal(false);\n            setEmailChangeData({\n                newEmail: '',\n                currentPassword: ''\n            });\n        } catch (error) {\n            console.error('Email change error:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('Failed to change email', error.message || 'Please try again.');\n        } finally{\n            setIsEmailLoading(false);\n        }\n    };\n    const handleAccountDeletion = async ()=>{\n        if (!user) return;\n        setLoading(true);\n        try {\n            // Call our account deletion API\n            const response = await fetch('/api/user/delete-account', {\n                method: 'DELETE',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || 'Failed to delete account');\n            }\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('Account deleted successfully. You will be redirected to the homepage.');\n            // Sign out and redirect\n            await supabase.auth.signOut();\n            setTimeout(()=>{\n                window.location.href = '/';\n            }, 2000);\n        } catch (error) {\n            console.error('Account deletion error:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(error.message || 'Failed to delete account. Please contact support.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Settings\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-1\",\n                            children: \"Manage your account settings and preferences\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 327,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"-mb-px flex space-x-8\",\n                    children: sidebarItems.map((item)=>{\n                        const Icon = item.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveSection(item.id),\n                            className: \"flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors \".concat(activeSection === item.id ? 'border-orange-500 text-orange-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 17\n                                }, this),\n                                item.label\n                            ]\n                        }, item.id, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 336,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 335,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl\",\n                children: [\n                    activeSection === 'account' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Email address\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: [\n                                                            \"Your email address is \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: user === null || user === void 0 ? void 0 : user.email\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 74\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 mt-1\",\n                                                        children: \"This is used for login and important notifications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-md transition-colors\",\n                                                onClick: ()=>setShowChangeEmailModal(true),\n                                                children: \"Change\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-6\",\n                                        children: \"Account Information\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-900 mb-1\",\n                                                                children: \"Full Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || (user === null || user === void 0 ? void 0 : (_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : _user_user_metadata1.first_name) + ' ' + (user === null || user === void 0 ? void 0 : (_user_user_metadata2 = user.user_metadata) === null || _user_user_metadata2 === void 0 ? void 0 : _user_user_metadata2.last_name) || 'Not set'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-900 mb-1\",\n                                                                children: \"Account Created\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: (user === null || user === void 0 ? void 0 : user.created_at) ? new Date(user.created_at).toLocaleDateString('en-US', {\n                                                                    year: 'numeric',\n                                                                    month: 'long',\n                                                                    day: 'numeric'\n                                                                }) : 'Unknown'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-gray-900 mb-1\",\n                                                            children: \"Account Usage\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1 text-sm text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        userStats.configCount,\n                                                                        \" configurations\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 404,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        userStats.apiKeyCount,\n                                                                        \" API keys\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 405,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        userStats.userApiKeyCount,\n                                                                        \" user-generated keys\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-6\",\n                                        children: \"Password\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handlePasswordChange,\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"currentPassword\",\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Current password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative mt-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                id: \"currentPassword\",\n                                                                type: showCurrentPassword ? \"text\" : \"password\",\n                                                                value: passwordData.currentPassword,\n                                                                onChange: (e)=>setPasswordData((prev)=>({\n                                                                            ...prev,\n                                                                            currentPassword: e.target.value\n                                                                        })),\n                                                                placeholder: \"••••••••••\",\n                                                                className: \"pr-10\",\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 420,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>setShowCurrentPassword(!showCurrentPassword),\n                                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                                                children: showCurrentPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 50\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 89\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 429,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                htmlFor: \"newPassword\",\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"New password\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                        id: \"newPassword\",\n                                                                        type: showNewPassword ? \"text\" : \"password\",\n                                                                        value: passwordData.newPassword,\n                                                                        onChange: (e)=>setPasswordData((prev)=>({\n                                                                                    ...prev,\n                                                                                    newPassword: e.target.value\n                                                                                })),\n                                                                        placeholder: \"••••••••••\",\n                                                                        className: \"pr-10\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 442,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>setShowNewPassword(!showNewPassword),\n                                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                                                        children: showNewPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 456,\n                                                                            columnNumber: 48\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 456,\n                                                                            columnNumber: 87\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 451,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                htmlFor: \"confirmPassword\",\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Confirm new password\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                        id: \"confirmPassword\",\n                                                                        type: showConfirmPassword ? \"text\" : \"password\",\n                                                                        value: passwordData.confirmPassword,\n                                                                        onChange: (e)=>setPasswordData((prev)=>({\n                                                                                    ...prev,\n                                                                                    confirmPassword: e.target.value\n                                                                                })),\n                                                                        placeholder: \"••••••••••\",\n                                                                        className: \"pr-10\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 463,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>setShowConfirmPassword(!showConfirmPassword),\n                                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                                                        children: showConfirmPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 477,\n                                                                            columnNumber: 52\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 477,\n                                                                            columnNumber: 91\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 472,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            \"Can't remember your current password? \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                                                onClick: ()=>setShowResetModal(true),\n                                                                children: \"Reset your password\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 484,\n                                                                columnNumber: 63\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"submit\",\n                                                        disabled: loading,\n                                                        className: \"bg-orange-600 hover:bg-orange-700\",\n                                                        children: loading ? 'Saving...' : 'Save password'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 15\n                    }, this),\n                    activeSection === 'notifications' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-6\",\n                                    children: \"Notification Preferences\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        Object.entries(notificationSettings).map((param)=>{\n                                            let [key, value] = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: [\n                                                                    key === 'emailNotifications' && 'Email Notifications',\n                                                                    key === 'securityAlerts' && 'Security Alerts',\n                                                                    key === 'usageAlerts' && 'Usage Alerts',\n                                                                    key === 'marketingEmails' && 'Marketing Emails'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 509,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500 mt-1\",\n                                                                children: [\n                                                                    key === 'emailNotifications' && 'Receive general email notifications',\n                                                                    key === 'securityAlerts' && 'Get notified about security events',\n                                                                    key === 'usageAlerts' && 'Alerts about API usage and limits',\n                                                                    key === 'marketingEmails' && 'Product updates and promotional content'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: value,\n                                                                onChange: (e)=>setNotificationSettings((prev)=>({\n                                                                            ...prev,\n                                                                            [key]: e.target.checked\n                                                                        })),\n                                                                className: \"sr-only peer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 523,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 529,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, key, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 23\n                                            }, this);\n                                        }),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pt-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: handleNotificationUpdate,\n                                                disabled: loading,\n                                                className: \"bg-orange-600 hover:bg-orange-700\",\n                                                children: loading ? 'Saving...' : 'Save preferences'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 533,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 503,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 502,\n                        columnNumber: 15\n                    }, this),\n                    activeSection === 'security' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"Security Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 547,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Secure\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-900 mb-2\",\n                                                        children: \"Password Security\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-3\",\n                                                        children: \"Change your password regularly for better security\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setActiveSection('account'),\n                                                        className: \"text-orange-600 border-orange-300 hover:bg-orange-50\",\n                                                        children: \"Change Password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-gray-900 mb-1\",\n                                                            children: \"Account Activity\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                userStats.configCount + userStats.apiKeyCount + userStats.userApiKeyCount,\n                                                                \" total resources created\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 572,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-gray-900 mb-1\",\n                                                            children: \"Account Created\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: (user === null || user === void 0 ? void 0 : user.created_at) ? new Date(user.created_at).toLocaleDateString('en-US', {\n                                                                year: 'numeric',\n                                                                month: 'long',\n                                                                day: 'numeric'\n                                                            }) : 'Unknown'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 554,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 545,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 544,\n                        columnNumber: 15\n                    }, this),\n                    activeSection === 'danger' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg border border-red-200 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-6 w-6 text-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-red-900\",\n                                            children: \"Danger Zone\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 rounded-lg p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-medium text-red-900 mb-3\",\n                                            children: \"Delete Account\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-700 mb-4\",\n                                            children: \"Permanently delete your RouKey account and all associated data. This action cannot be undone.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white border border-red-200 rounded-md p-4 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"text-sm font-medium text-red-900 mb-2\",\n                                                    children: \"This will permanently delete:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 606,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-sm text-red-700 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                \"• \",\n                                                                userStats.configCount,\n                                                                \" API configurations\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 608,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                \"• \",\n                                                                userStats.apiKeyCount,\n                                                                \" API keys\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 609,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                \"• \",\n                                                                userStats.userApiKeyCount,\n                                                                \" user-generated API keys\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 610,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• All usage logs and analytics\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Your subscription and billing information\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 612,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 607,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 605,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            className: \"text-red-600 border-red-300 hover:bg-red-100 hover:border-red-400\",\n                                            onClick: ()=>setShowDeleteModal(true),\n                                            disabled: loading,\n                                            children: \"Delete my account\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 615,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 600,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 593,\n                        columnNumber: 15\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 358,\n                columnNumber: 7\n            }, this),\n            showChangeEmailModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-2xl max-w-md w-full p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-5 w-5 text-orange-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 635,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 634,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Change Email Address\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 637,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 633,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"Enter your new email address and current password to change your email. You'll need to verify both your old and new email addresses.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 640,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: (e)=>{\n                                e.preventDefault();\n                                handleEmailChange();\n                            },\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"newEmail\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"New Email Address\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 647,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"newEmail\",\n                                            type: \"email\",\n                                            value: emailChangeData.newEmail,\n                                            onChange: (e)=>setEmailChangeData((prev)=>({\n                                                        ...prev,\n                                                        newEmail: e.target.value\n                                                    })),\n                                            placeholder: \"Enter your new email address\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 650,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 646,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"currentPasswordEmail\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Current Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 662,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"currentPasswordEmail\",\n                                            type: \"password\",\n                                            value: emailChangeData.currentPassword,\n                                            onChange: (e)=>setEmailChangeData((prev)=>({\n                                                        ...prev,\n                                                        currentPassword: e.target.value\n                                                    })),\n                                            placeholder: \"Enter your current password\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 665,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 661,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-orange-50 border border-orange-200 rounded-lg p-4 mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium text-orange-900 mb-2\",\n                                            children: \"What happens next:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 677,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm text-orange-700 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• You'll receive confirmation emails at both addresses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Click the links in both emails to confirm the change\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 680,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Your email will be updated once both are verified\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 681,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• You can continue using your current email until then\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 682,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 678,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 676,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>{\n                                                setShowChangeEmailModal(false);\n                                                setEmailChangeData({\n                                                    newEmail: '',\n                                                    currentPassword: ''\n                                                });\n                                            },\n                                            className: \"flex-1\",\n                                            disabled: isEmailLoading,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 687,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"submit\",\n                                            disabled: isEmailLoading,\n                                            className: \"flex-1 bg-orange-600 hover:bg-orange-700 text-white\",\n                                            children: isEmailLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 706,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Changing...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 21\n                                            }, this) : 'Change Email'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 699,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 686,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 645,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 632,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 631,\n                columnNumber: 9\n            }, this),\n            showResetModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-2xl max-w-md w-full p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-5 w-5 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 725,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 724,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Reset Your Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 727,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 723,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: [\n                                \"We'll send a password reset link to \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: user === null || user === void 0 ? void 0 : user.email\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 731,\n                                    columnNumber: 51\n                                }, this),\n                                \". You can use this link to create a new password.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 730,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-blue-900 mb-2\",\n                                    children: \"What happens next:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 736,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-blue-700 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• You'll receive an email with a secure reset link\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 738,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Click the link to create a new password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 739,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Your current password will remain active until you complete the reset\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 740,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 737,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 735,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowResetModal(false),\n                                    className: \"flex-1\",\n                                    disabled: isResetLoading,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 745,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handlePasswordReset,\n                                    disabled: isResetLoading,\n                                    className: \"flex-1 bg-blue-600 hover:bg-blue-700 text-white\",\n                                    children: isResetLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 760,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Sending...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 759,\n                                        columnNumber: 19\n                                    }, this) : 'Send Reset Link'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 753,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 744,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 722,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 721,\n                columnNumber: 9\n            }, this),\n            showDeleteModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-2xl max-w-md w-full p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-6 w-6 text-red-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 777,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Delete Account\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 778,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 776,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Are you absolutely sure you want to delete your account? This action cannot be undone.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 781,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-red-900 mb-2\",\n                                    children: \"This will permanently delete:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 786,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-red-700 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                userStats.configCount,\n                                                \" API configurations\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 788,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                userStats.apiKeyCount,\n                                                \" API keys\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 789,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                userStats.userApiKeyCount,\n                                                \" user-generated API keys\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 790,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• All usage logs and analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 791,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Your subscription and billing information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 792,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 787,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 785,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowDeleteModal(false),\n                                    className: \"flex-1\",\n                                    disabled: loading,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 797,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleAccountDeletion,\n                                    disabled: loading,\n                                    className: \"flex-1 bg-red-600 hover:bg-red-700 text-white\",\n                                    children: loading ? 'Deleting...' : 'Delete Account'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 805,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 796,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 775,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 774,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 325,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsPage, \"NAy9MrHPP6619V7LFxVDlZM81Fo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_8__.useSubscription\n    ];\n});\n_c = SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/settings/page.tsx\n"));

/***/ })

});