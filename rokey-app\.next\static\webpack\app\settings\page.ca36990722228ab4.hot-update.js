"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/settings/page",{

/***/ "(app-pages-browser)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SettingsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction SettingsPage() {\n    var _user_user_metadata, _user_user_metadata1, _user_user_metadata2;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_7__.useSubscription)();\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_6__.createSupabaseBrowserClient)();\n    const { success, error: toastError } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('account');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCurrentPassword, setShowCurrentPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNewPassword, setShowNewPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDeleteModal, setShowDeleteModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showResetModal, setShowResetModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isResetLoading, setIsResetLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showChangeEmailModal, setShowChangeEmailModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEmailLoading, setIsEmailLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [emailChangeData, setEmailChangeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        newEmail: '',\n        currentPassword: ''\n    });\n    const [passwordData, setPasswordData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n    });\n    const [notificationSettings, setNotificationSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        emailNotifications: true,\n        securityAlerts: true,\n        usageAlerts: true,\n        marketingEmails: false\n    });\n    const [userStats, setUserStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        configCount: 0,\n        apiKeyCount: 0,\n        userApiKeyCount: 0\n    });\n    const sidebarItems = [\n        {\n            id: 'account',\n            label: 'Account settings',\n            icon: _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            id: 'notifications',\n            label: 'Notifications',\n            icon: _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            id: 'security',\n            label: 'Security',\n            icon: _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            id: 'danger',\n            label: 'Danger zone',\n            icon: _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        }\n    ];\n    // Load user data and stats\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsPage.useEffect\": ()=>{\n            if (user) {\n                loadUserStats();\n                loadNotificationSettings();\n            }\n        }\n    }[\"SettingsPage.useEffect\"], [\n        user\n    ]);\n    const loadUserStats = async ()=>{\n        try {\n            // Get configuration count\n            const configResponse = await fetch('/api/custom-configs');\n            const configData = configResponse.ok ? await configResponse.json() : [];\n            const configs = Array.isArray(configData) ? configData : [];\n            // Get user API keys count\n            const userKeysResponse = await fetch('/api/user-api-keys');\n            const userKeysData = userKeysResponse.ok ? await userKeysResponse.json() : {\n                api_keys: []\n            };\n            const userKeys = userKeysData.api_keys || [];\n            // Get total API keys count across all configs\n            let totalApiKeys = 0;\n            for (const config of configs){\n                try {\n                    const keysResponse = await fetch(\"/api/custom-configs/\".concat(config.id, \"/keys\"));\n                    const keysData = keysResponse.ok ? await keysResponse.json() : {\n                        api_keys: []\n                    };\n                    totalApiKeys += (keysData.api_keys || []).length;\n                } catch (error) {\n                    console.error('Error loading keys for config:', config.id, error);\n                }\n            }\n            setUserStats({\n                configCount: configs.length,\n                apiKeyCount: totalApiKeys,\n                userApiKeyCount: userKeys.length\n            });\n        } catch (error) {\n            console.error('Error loading user stats:', error);\n        }\n    };\n    const loadNotificationSettings = async ()=>{\n        try {\n            // Load from user metadata or local storage\n            const savedSettings = localStorage.getItem('notification_settings');\n            if (savedSettings) {\n                setNotificationSettings(JSON.parse(savedSettings));\n            }\n        } catch (error) {\n            console.error('Error loading notification settings:', error);\n        }\n    };\n    const handlePasswordChange = async (e)=>{\n        e.preventDefault();\n        if (!passwordData.currentPassword.trim()) {\n            toastError('Current password is required');\n            return;\n        }\n        if (!passwordData.newPassword.trim()) {\n            toastError('New password is required');\n            return;\n        }\n        if (passwordData.newPassword !== passwordData.confirmPassword) {\n            toastError('New passwords do not match');\n            return;\n        }\n        if (passwordData.newPassword.length < 8) {\n            toastError('Password must be at least 8 characters long');\n            return;\n        }\n        if (passwordData.newPassword === passwordData.currentPassword) {\n            toastError('New password must be different from current password');\n            return;\n        }\n        setLoading(true);\n        try {\n            // First verify current password by attempting to sign in\n            const { error: signInError } = await supabase.auth.signInWithPassword({\n                email: (user === null || user === void 0 ? void 0 : user.email) || '',\n                password: passwordData.currentPassword\n            });\n            if (signInError) {\n                toast.error('Current password is incorrect');\n                setLoading(false);\n                return;\n            }\n            // If current password is correct, update to new password\n            const { error: updateError } = await supabase.auth.updateUser({\n                password: passwordData.newPassword\n            });\n            if (updateError) throw updateError;\n            toast.success('Password updated successfully');\n            setPasswordData({\n                currentPassword: '',\n                newPassword: '',\n                confirmPassword: ''\n            });\n        } catch (error) {\n            console.error('Password update error:', error);\n            toast.error(error.message || 'Failed to update password');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleNotificationUpdate = async ()=>{\n        setLoading(true);\n        try {\n            // Save to local storage and user metadata\n            localStorage.setItem('notification_settings', JSON.stringify(notificationSettings));\n            // Also save to user metadata for persistence across devices\n            const { error } = await supabase.auth.updateUser({\n                data: {\n                    notification_preferences: notificationSettings\n                }\n            });\n            if (error) throw error;\n            toast.success('Notification preferences saved successfully');\n        } catch (error) {\n            console.error('Notification update error:', error);\n            toast.error('Failed to save notification preferences');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handlePasswordReset = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) {\n            toast.error('No email address found');\n            return;\n        }\n        setIsResetLoading(true);\n        try {\n            const { error } = await supabase.auth.resetPasswordForEmail(user.email, {\n                redirectTo: \"\".concat(window.location.origin, \"/auth/reset-password\")\n            });\n            if (error) throw error;\n            toast.success('Password reset email sent!', 'Check your inbox for instructions to reset your password.');\n            setShowResetModal(false);\n        } catch (error) {\n            console.error('Password reset error:', error);\n            toast.error('Failed to send reset email', error.message || 'Please try again.');\n        } finally{\n            setIsResetLoading(false);\n        }\n    };\n    const handleEmailChange = async ()=>{\n        if (!emailChangeData.newEmail.trim()) {\n            toast.error('Please enter a new email address');\n            return;\n        }\n        if (!emailChangeData.currentPassword.trim()) {\n            toast.error('Current password is required to change email');\n            return;\n        }\n        if (emailChangeData.newEmail === (user === null || user === void 0 ? void 0 : user.email)) {\n            toast.error('New email must be different from current email');\n            return;\n        }\n        // Basic email validation\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(emailChangeData.newEmail)) {\n            toast.error('Please enter a valid email address');\n            return;\n        }\n        setIsEmailLoading(true);\n        try {\n            // First verify current password\n            const { error: signInError } = await supabase.auth.signInWithPassword({\n                email: (user === null || user === void 0 ? void 0 : user.email) || '',\n                password: emailChangeData.currentPassword\n            });\n            if (signInError) {\n                toast.error('Current password is incorrect');\n                setIsEmailLoading(false);\n                return;\n            }\n            // Update email\n            const { error: updateError } = await supabase.auth.updateUser({\n                email: emailChangeData.newEmail\n            });\n            if (updateError) throw updateError;\n            toast.success('Email change initiated!', 'Check both your old and new email addresses for confirmation instructions.');\n            setShowChangeEmailModal(false);\n            setEmailChangeData({\n                newEmail: '',\n                currentPassword: ''\n            });\n        } catch (error) {\n            console.error('Email change error:', error);\n            toast.error('Failed to change email', error.message || 'Please try again.');\n        } finally{\n            setIsEmailLoading(false);\n        }\n    };\n    const handleAccountDeletion = async ()=>{\n        if (!user) return;\n        setLoading(true);\n        try {\n            // Call our account deletion API\n            const response = await fetch('/api/user/delete-account', {\n                method: 'DELETE',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || 'Failed to delete account');\n            }\n            toast.success('Account deleted successfully. You will be redirected to the homepage.');\n            // Sign out and redirect\n            await supabase.auth.signOut();\n            setTimeout(()=>{\n                window.location.href = '/';\n            }, 2000);\n        } catch (error) {\n            console.error('Account deletion error:', error);\n            toast.error(error.message || 'Failed to delete account. Please contact support.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Settings\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-1\",\n                            children: \"Manage your account settings and preferences\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 327,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"-mb-px flex space-x-8\",\n                    children: sidebarItems.map((item)=>{\n                        const Icon = item.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveSection(item.id),\n                            className: \"flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors \".concat(activeSection === item.id ? 'border-orange-500 text-orange-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 17\n                                }, this),\n                                item.label\n                            ]\n                        }, item.id, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 336,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 335,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl\",\n                children: [\n                    activeSection === 'account' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Email address\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: [\n                                                            \"Your email address is \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: user === null || user === void 0 ? void 0 : user.email\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 74\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 mt-1\",\n                                                        children: \"This is used for login and important notifications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-md transition-colors\",\n                                                onClick: ()=>setShowChangeEmailModal(true),\n                                                children: \"Change\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-6\",\n                                        children: \"Account Information\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-900 mb-1\",\n                                                                children: \"Full Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || (user === null || user === void 0 ? void 0 : (_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : _user_user_metadata1.first_name) + ' ' + (user === null || user === void 0 ? void 0 : (_user_user_metadata2 = user.user_metadata) === null || _user_user_metadata2 === void 0 ? void 0 : _user_user_metadata2.last_name) || 'Not set'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-900 mb-1\",\n                                                                children: \"Account Created\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: (user === null || user === void 0 ? void 0 : user.created_at) ? new Date(user.created_at).toLocaleDateString('en-US', {\n                                                                    year: 'numeric',\n                                                                    month: 'long',\n                                                                    day: 'numeric'\n                                                                }) : 'Unknown'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-gray-900 mb-1\",\n                                                            children: \"Account Usage\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1 text-sm text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        userStats.configCount,\n                                                                        \" configurations\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 404,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        userStats.apiKeyCount,\n                                                                        \" API keys\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 405,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        userStats.userApiKeyCount,\n                                                                        \" user-generated keys\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-6\",\n                                        children: \"Password\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handlePasswordChange,\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"currentPassword\",\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Current password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative mt-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                id: \"currentPassword\",\n                                                                type: showCurrentPassword ? \"text\" : \"password\",\n                                                                value: passwordData.currentPassword,\n                                                                onChange: (e)=>setPasswordData((prev)=>({\n                                                                            ...prev,\n                                                                            currentPassword: e.target.value\n                                                                        })),\n                                                                placeholder: \"••••••••••\",\n                                                                className: \"w-full pr-10 px-3 py-2 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 bg-white focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200\",\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 420,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>setShowCurrentPassword(!showCurrentPassword),\n                                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                                                children: showCurrentPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 50\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 89\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 429,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"newPassword\",\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"New password\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"newPassword\",\n                                                                        type: showNewPassword ? \"text\" : \"password\",\n                                                                        value: passwordData.newPassword,\n                                                                        onChange: (e)=>setPasswordData((prev)=>({\n                                                                                    ...prev,\n                                                                                    newPassword: e.target.value\n                                                                                })),\n                                                                        placeholder: \"••••••••••\",\n                                                                        className: \"w-full pr-10 px-3 py-2 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 bg-white focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 442,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>setShowNewPassword(!showNewPassword),\n                                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                                                        children: showNewPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 456,\n                                                                            columnNumber: 48\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 456,\n                                                                            columnNumber: 87\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 451,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"confirmPassword\",\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"Confirm new password\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"confirmPassword\",\n                                                                        type: showConfirmPassword ? \"text\" : \"password\",\n                                                                        value: passwordData.confirmPassword,\n                                                                        onChange: (e)=>setPasswordData((prev)=>({\n                                                                                    ...prev,\n                                                                                    confirmPassword: e.target.value\n                                                                                })),\n                                                                        placeholder: \"••••••••••\",\n                                                                        className: \"w-full pr-10 px-3 py-2 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 bg-white focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 463,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>setShowConfirmPassword(!showConfirmPassword),\n                                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                                                        children: showConfirmPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 477,\n                                                                            columnNumber: 52\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 477,\n                                                                            columnNumber: 91\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 472,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            \"Can't remember your current password? \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                                                onClick: ()=>setShowResetModal(true),\n                                                                children: \"Reset your password\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 484,\n                                                                columnNumber: 63\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"submit\",\n                                                        disabled: loading,\n                                                        className: \"bg-orange-600 hover:bg-orange-700\",\n                                                        children: loading ? 'Saving...' : 'Save password'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 15\n                    }, this),\n                    activeSection === 'notifications' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-6\",\n                                    children: \"Notification Preferences\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        Object.entries(notificationSettings).map((param)=>{\n                                            let [key, value] = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: [\n                                                                    key === 'emailNotifications' && 'Email Notifications',\n                                                                    key === 'securityAlerts' && 'Security Alerts',\n                                                                    key === 'usageAlerts' && 'Usage Alerts',\n                                                                    key === 'marketingEmails' && 'Marketing Emails'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 509,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500 mt-1\",\n                                                                children: [\n                                                                    key === 'emailNotifications' && 'Receive general email notifications',\n                                                                    key === 'securityAlerts' && 'Get notified about security events',\n                                                                    key === 'usageAlerts' && 'Alerts about API usage and limits',\n                                                                    key === 'marketingEmails' && 'Product updates and promotional content'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: value,\n                                                                onChange: (e)=>setNotificationSettings((prev)=>({\n                                                                            ...prev,\n                                                                            [key]: e.target.checked\n                                                                        })),\n                                                                className: \"sr-only peer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 523,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 529,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, key, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 23\n                                            }, this);\n                                        }),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pt-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: handleNotificationUpdate,\n                                                disabled: loading,\n                                                className: \"bg-orange-600 hover:bg-orange-700\",\n                                                children: loading ? 'Saving...' : 'Save preferences'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 533,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 503,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 502,\n                        columnNumber: 15\n                    }, this),\n                    activeSection === 'security' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"Security Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 547,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Secure\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-900 mb-2\",\n                                                        children: \"Password Security\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-3\",\n                                                        children: \"Change your password regularly for better security\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setActiveSection('account'),\n                                                        className: \"text-orange-600 border-orange-300 hover:bg-orange-50\",\n                                                        children: \"Change Password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-gray-900 mb-1\",\n                                                            children: \"Account Activity\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                userStats.configCount + userStats.apiKeyCount + userStats.userApiKeyCount,\n                                                                \" total resources created\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 572,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-gray-900 mb-1\",\n                                                            children: \"Account Created\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: (user === null || user === void 0 ? void 0 : user.created_at) ? new Date(user.created_at).toLocaleDateString('en-US', {\n                                                                year: 'numeric',\n                                                                month: 'long',\n                                                                day: 'numeric'\n                                                            }) : 'Unknown'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 554,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 545,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 544,\n                        columnNumber: 15\n                    }, this),\n                    activeSection === 'danger' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg border border-red-200 p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-6 w-6 text-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-red-900\",\n                                            children: \"Danger Zone\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 rounded-lg p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-medium text-red-900 mb-3\",\n                                            children: \"Delete Account\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-700 mb-4\",\n                                            children: \"Permanently delete your RouKey account and all associated data. This action cannot be undone.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white border border-red-200 rounded-md p-4 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"text-sm font-medium text-red-900 mb-2\",\n                                                    children: \"This will permanently delete:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 606,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-sm text-red-700 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                \"• \",\n                                                                userStats.configCount,\n                                                                \" API configurations\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 608,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                \"• \",\n                                                                userStats.apiKeyCount,\n                                                                \" API keys\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 609,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                \"• \",\n                                                                userStats.userApiKeyCount,\n                                                                \" user-generated API keys\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 610,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• All usage logs and analytics\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Your subscription and billing information\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 612,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 607,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 605,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            className: \"text-red-600 border-red-300 hover:bg-red-100 hover:border-red-400\",\n                                            onClick: ()=>setShowDeleteModal(true),\n                                            disabled: loading,\n                                            children: \"Delete my account\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 615,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 600,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 593,\n                        columnNumber: 15\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 358,\n                columnNumber: 7\n            }, this),\n            showChangeEmailModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-2xl max-w-md w-full p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5 text-orange-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 635,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 634,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Change Email Address\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 637,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 633,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"Enter your new email address and current password to change your email. You'll need to verify both your old and new email addresses.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 640,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: (e)=>{\n                                e.preventDefault();\n                                handleEmailChange();\n                            },\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"newEmail\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"New Email Address\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 647,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"newEmail\",\n                                            type: \"email\",\n                                            value: emailChangeData.newEmail,\n                                            onChange: (e)=>setEmailChangeData((prev)=>({\n                                                        ...prev,\n                                                        newEmail: e.target.value\n                                                    })),\n                                            placeholder: \"Enter your new email address\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 650,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 646,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"currentPasswordEmail\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Current Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 662,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"currentPasswordEmail\",\n                                            type: \"password\",\n                                            value: emailChangeData.currentPassword,\n                                            onChange: (e)=>setEmailChangeData((prev)=>({\n                                                        ...prev,\n                                                        currentPassword: e.target.value\n                                                    })),\n                                            placeholder: \"Enter your current password\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 665,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 661,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-orange-50 border border-orange-200 rounded-lg p-4 mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium text-orange-900 mb-2\",\n                                            children: \"What happens next:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 677,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm text-orange-700 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• You'll receive confirmation emails at both addresses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Click the links in both emails to confirm the change\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 680,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Your email will be updated once both are verified\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 681,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• You can continue using your current email until then\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 682,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 678,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 676,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>{\n                                                setShowChangeEmailModal(false);\n                                                setEmailChangeData({\n                                                    newEmail: '',\n                                                    currentPassword: ''\n                                                });\n                                            },\n                                            className: \"flex-1\",\n                                            disabled: isEmailLoading,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 687,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"submit\",\n                                            disabled: isEmailLoading,\n                                            className: \"flex-1 bg-orange-600 hover:bg-orange-700 text-white\",\n                                            children: isEmailLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 706,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Changing...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 21\n                                            }, this) : 'Change Email'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 699,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 686,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 645,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 632,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 631,\n                columnNumber: 9\n            }, this),\n            showResetModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-2xl max-w-md w-full p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 725,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 724,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Reset Your Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 727,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 723,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: [\n                                \"We'll send a password reset link to \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: user === null || user === void 0 ? void 0 : user.email\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 731,\n                                    columnNumber: 51\n                                }, this),\n                                \". You can use this link to create a new password.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 730,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-blue-900 mb-2\",\n                                    children: \"What happens next:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 736,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-blue-700 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• You'll receive an email with a secure reset link\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 738,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Click the link to create a new password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 739,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Your current password will remain active until you complete the reset\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 740,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 737,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 735,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowResetModal(false),\n                                    className: \"flex-1\",\n                                    disabled: isResetLoading,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 745,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handlePasswordReset,\n                                    disabled: isResetLoading,\n                                    className: \"flex-1 bg-blue-600 hover:bg-blue-700 text-white\",\n                                    children: isResetLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 760,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Sending...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 759,\n                                        columnNumber: 19\n                                    }, this) : 'Send Reset Link'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 753,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 744,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 722,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 721,\n                columnNumber: 9\n            }, this),\n            showDeleteModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-2xl max-w-md w-full p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-6 w-6 text-red-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 777,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Delete Account\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 778,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 776,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Are you absolutely sure you want to delete your account? This action cannot be undone.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 781,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-red-900 mb-2\",\n                                    children: \"This will permanently delete:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 786,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-red-700 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                userStats.configCount,\n                                                \" API configurations\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 788,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                userStats.apiKeyCount,\n                                                \" API keys\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 789,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                userStats.userApiKeyCount,\n                                                \" user-generated API keys\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 790,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• All usage logs and analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 791,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Your subscription and billing information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 792,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 787,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 785,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowDeleteModal(false),\n                                    className: \"flex-1\",\n                                    disabled: loading,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 797,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleAccountDeletion,\n                                    disabled: loading,\n                                    className: \"flex-1 bg-red-600 hover:bg-red-700 text-white\",\n                                    children: loading ? 'Deleting...' : 'Delete Account'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 805,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 796,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 775,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 774,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 325,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsPage, \"mrO/1whaEDYY8En15/HfYhebKMM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_7__.useSubscription,\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvc2V0dGluZ3MvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUU0QztBQUNBO0FBVVA7QUFDVztBQUNGO0FBQ0c7QUFDbUI7QUFDVjtBQUUzQyxTQUFTZTtRQTZXR0MscUJBQWtDQSxzQkFBd0NBOztJQTVXbkcsTUFBTUMsU0FBU2YsMERBQVNBO0lBQ3hCLE1BQU0sRUFBRWMsSUFBSSxFQUFFLEdBQUdGLHVFQUFlQTtJQUNoQyxNQUFNSSxXQUFXTCxpRkFBMkJBO0lBQzVDLE1BQU0sRUFBRU0sT0FBTyxFQUFFQyxPQUFPQyxVQUFVLEVBQUUsR0FBR1QsOERBQVFBO0lBRS9DLE1BQU0sQ0FBQ1UsZUFBZUMsaUJBQWlCLEdBQUd2QiwrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUN3QixTQUFTQyxXQUFXLEdBQUd6QiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUMwQixxQkFBcUJDLHVCQUF1QixHQUFHM0IsK0NBQVFBLENBQUM7SUFDL0QsTUFBTSxDQUFDNEIsaUJBQWlCQyxtQkFBbUIsR0FBRzdCLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQzhCLHFCQUFxQkMsdUJBQXVCLEdBQUcvQiwrQ0FBUUEsQ0FBQztJQUMvRCxNQUFNLENBQUNnQyxpQkFBaUJDLG1CQUFtQixHQUFHakMsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDa0MsZ0JBQWdCQyxrQkFBa0IsR0FBR25DLCtDQUFRQSxDQUFDO0lBQ3JELE1BQU0sQ0FBQ29DLGdCQUFnQkMsa0JBQWtCLEdBQUdyQywrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUNzQyxzQkFBc0JDLHdCQUF3QixHQUFHdkMsK0NBQVFBLENBQUM7SUFDakUsTUFBTSxDQUFDd0MsZ0JBQWdCQyxrQkFBa0IsR0FBR3pDLCtDQUFRQSxDQUFDO0lBQ3JELE1BQU0sQ0FBQzBDLGlCQUFpQkMsbUJBQW1CLEdBQUczQywrQ0FBUUEsQ0FBQztRQUNyRDRDLFVBQVU7UUFDVkMsaUJBQWlCO0lBQ25CO0lBRUEsTUFBTSxDQUFDQyxjQUFjQyxnQkFBZ0IsR0FBRy9DLCtDQUFRQSxDQUFDO1FBQy9DNkMsaUJBQWlCO1FBQ2pCRyxhQUFhO1FBQ2JDLGlCQUFpQjtJQUNuQjtJQUVBLE1BQU0sQ0FBQ0Msc0JBQXNCQyx3QkFBd0IsR0FBR25ELCtDQUFRQSxDQUFDO1FBQy9Eb0Qsb0JBQW9CO1FBQ3BCQyxnQkFBZ0I7UUFDaEJDLGFBQWE7UUFDYkMsaUJBQWlCO0lBQ25CO0lBRUEsTUFBTSxDQUFDQyxXQUFXQyxhQUFhLEdBQUd6RCwrQ0FBUUEsQ0FBQztRQUN6QzBELGFBQWE7UUFDYkMsYUFBYTtRQUNiQyxpQkFBaUI7SUFDbkI7SUFFQSxNQUFNQyxlQUFlO1FBQ25CO1lBQUVDLElBQUk7WUFBV0MsT0FBTztZQUFvQkMsTUFBTTdELHVLQUFRQTtRQUFDO1FBQzNEO1lBQUUyRCxJQUFJO1lBQWlCQyxPQUFPO1lBQWlCQyxNQUFNM0QsdUtBQVFBO1FBQUM7UUFDOUQ7WUFBRXlELElBQUk7WUFBWUMsT0FBTztZQUFZQyxNQUFNNUQsd0tBQWVBO1FBQUM7UUFDM0Q7WUFBRTBELElBQUk7WUFBVUMsT0FBTztZQUFlQyxNQUFNeEQsd0tBQVNBO1FBQUM7S0FDdkQ7SUFFRCwyQkFBMkI7SUFDM0JQLGdEQUFTQTtrQ0FBQztZQUNSLElBQUllLE1BQU07Z0JBQ1JpRDtnQkFDQUM7WUFDRjtRQUNGO2lDQUFHO1FBQUNsRDtLQUFLO0lBRVQsTUFBTWlELGdCQUFnQjtRQUNwQixJQUFJO1lBQ0YsMEJBQTBCO1lBQzFCLE1BQU1FLGlCQUFpQixNQUFNQyxNQUFNO1lBQ25DLE1BQU1DLGFBQWFGLGVBQWVHLEVBQUUsR0FBRyxNQUFNSCxlQUFlSSxJQUFJLEtBQUssRUFBRTtZQUN2RSxNQUFNQyxVQUFVQyxNQUFNQyxPQUFPLENBQUNMLGNBQWNBLGFBQWEsRUFBRTtZQUUzRCwwQkFBMEI7WUFDMUIsTUFBTU0sbUJBQW1CLE1BQU1QLE1BQU07WUFDckMsTUFBTVEsZUFBZUQsaUJBQWlCTCxFQUFFLEdBQUcsTUFBTUssaUJBQWlCSixJQUFJLEtBQUs7Z0JBQUVNLFVBQVUsRUFBRTtZQUFDO1lBQzFGLE1BQU1DLFdBQVdGLGFBQWFDLFFBQVEsSUFBSSxFQUFFO1lBRTVDLDhDQUE4QztZQUM5QyxJQUFJRSxlQUFlO1lBQ25CLEtBQUssTUFBTUMsVUFBVVIsUUFBUztnQkFDNUIsSUFBSTtvQkFDRixNQUFNUyxlQUFlLE1BQU1iLE1BQU0sdUJBQWlDLE9BQVZZLE9BQU9sQixFQUFFLEVBQUM7b0JBQ2xFLE1BQU1vQixXQUFXRCxhQUFhWCxFQUFFLEdBQUcsTUFBTVcsYUFBYVYsSUFBSSxLQUFLO3dCQUFFTSxVQUFVLEVBQUU7b0JBQUM7b0JBQzlFRSxnQkFBZ0IsQ0FBQ0csU0FBU0wsUUFBUSxJQUFJLEVBQUUsRUFBRU0sTUFBTTtnQkFDbEQsRUFBRSxPQUFPL0QsT0FBTztvQkFDZGdFLFFBQVFoRSxLQUFLLENBQUMsa0NBQWtDNEQsT0FBT2xCLEVBQUUsRUFBRTFDO2dCQUM3RDtZQUNGO1lBRUFxQyxhQUFhO2dCQUNYQyxhQUFhYyxRQUFRVyxNQUFNO2dCQUMzQnhCLGFBQWFvQjtnQkFDYm5CLGlCQUFpQmtCLFNBQVNLLE1BQU07WUFDbEM7UUFDRixFQUFFLE9BQU8vRCxPQUFPO1lBQ2RnRSxRQUFRaEUsS0FBSyxDQUFDLDZCQUE2QkE7UUFDN0M7SUFDRjtJQUVBLE1BQU04QywyQkFBMkI7UUFDL0IsSUFBSTtZQUNGLDJDQUEyQztZQUMzQyxNQUFNbUIsZ0JBQWdCQyxhQUFhQyxPQUFPLENBQUM7WUFDM0MsSUFBSUYsZUFBZTtnQkFDakJsQyx3QkFBd0JxQyxLQUFLQyxLQUFLLENBQUNKO1lBQ3JDO1FBQ0YsRUFBRSxPQUFPakUsT0FBTztZQUNkZ0UsUUFBUWhFLEtBQUssQ0FBQyx3Q0FBd0NBO1FBQ3hEO0lBQ0Y7SUFFQSxNQUFNc0UsdUJBQXVCLE9BQU9DO1FBQ2xDQSxFQUFFQyxjQUFjO1FBRWhCLElBQUksQ0FBQzlDLGFBQWFELGVBQWUsQ0FBQ2dELElBQUksSUFBSTtZQUN4Q3hFLFdBQVc7WUFDWDtRQUNGO1FBRUEsSUFBSSxDQUFDeUIsYUFBYUUsV0FBVyxDQUFDNkMsSUFBSSxJQUFJO1lBQ3BDeEUsV0FBVztZQUNYO1FBQ0Y7UUFFQSxJQUFJeUIsYUFBYUUsV0FBVyxLQUFLRixhQUFhRyxlQUFlLEVBQUU7WUFDN0Q1QixXQUFXO1lBQ1g7UUFDRjtRQUVBLElBQUl5QixhQUFhRSxXQUFXLENBQUNtQyxNQUFNLEdBQUcsR0FBRztZQUN2QzlELFdBQVc7WUFDWDtRQUNGO1FBRUEsSUFBSXlCLGFBQWFFLFdBQVcsS0FBS0YsYUFBYUQsZUFBZSxFQUFFO1lBQzdEeEIsV0FBVztZQUNYO1FBQ0Y7UUFFQUksV0FBVztRQUVYLElBQUk7WUFDRix5REFBeUQ7WUFDekQsTUFBTSxFQUFFTCxPQUFPMEUsV0FBVyxFQUFFLEdBQUcsTUFBTTVFLFNBQVM2RSxJQUFJLENBQUNDLGtCQUFrQixDQUFDO2dCQUNwRUMsT0FBT2pGLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTWlGLEtBQUssS0FBSTtnQkFDdEJDLFVBQVVwRCxhQUFhRCxlQUFlO1lBQ3hDO1lBRUEsSUFBSWlELGFBQWE7Z0JBQ2ZLLE1BQU0vRSxLQUFLLENBQUM7Z0JBQ1pLLFdBQVc7Z0JBQ1g7WUFDRjtZQUVBLHlEQUF5RDtZQUN6RCxNQUFNLEVBQUVMLE9BQU9nRixXQUFXLEVBQUUsR0FBRyxNQUFNbEYsU0FBUzZFLElBQUksQ0FBQ00sVUFBVSxDQUFDO2dCQUM1REgsVUFBVXBELGFBQWFFLFdBQVc7WUFDcEM7WUFFQSxJQUFJb0QsYUFBYSxNQUFNQTtZQUV2QkQsTUFBTWhGLE9BQU8sQ0FBQztZQUNkNEIsZ0JBQWdCO2dCQUNkRixpQkFBaUI7Z0JBQ2pCRyxhQUFhO2dCQUNiQyxpQkFBaUI7WUFDbkI7UUFDRixFQUFFLE9BQU83QixPQUFZO1lBQ25CZ0UsUUFBUWhFLEtBQUssQ0FBQywwQkFBMEJBO1lBQ3hDK0UsTUFBTS9FLEtBQUssQ0FBQ0EsTUFBTWtGLE9BQU8sSUFBSTtRQUMvQixTQUFVO1lBQ1I3RSxXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU04RSwyQkFBMkI7UUFDL0I5RSxXQUFXO1FBRVgsSUFBSTtZQUNGLDBDQUEwQztZQUMxQzZELGFBQWFrQixPQUFPLENBQUMseUJBQXlCaEIsS0FBS2lCLFNBQVMsQ0FBQ3ZEO1lBRTdELDREQUE0RDtZQUM1RCxNQUFNLEVBQUU5QixLQUFLLEVBQUUsR0FBRyxNQUFNRixTQUFTNkUsSUFBSSxDQUFDTSxVQUFVLENBQUM7Z0JBQy9DSyxNQUFNO29CQUNKQywwQkFBMEJ6RDtnQkFDNUI7WUFDRjtZQUVBLElBQUk5QixPQUFPLE1BQU1BO1lBRWpCK0UsTUFBTWhGLE9BQU8sQ0FBQztRQUNoQixFQUFFLE9BQU9DLE9BQVk7WUFDbkJnRSxRQUFRaEUsS0FBSyxDQUFDLDhCQUE4QkE7WUFDNUMrRSxNQUFNL0UsS0FBSyxDQUFDO1FBQ2QsU0FBVTtZQUNSSyxXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU1tRixzQkFBc0I7UUFDMUIsSUFBSSxFQUFDNUYsaUJBQUFBLDJCQUFBQSxLQUFNaUYsS0FBSyxHQUFFO1lBQ2hCRSxNQUFNL0UsS0FBSyxDQUFDO1lBQ1o7UUFDRjtRQUVBaUIsa0JBQWtCO1FBQ2xCLElBQUk7WUFDRixNQUFNLEVBQUVqQixLQUFLLEVBQUUsR0FBRyxNQUFNRixTQUFTNkUsSUFBSSxDQUFDYyxxQkFBcUIsQ0FBQzdGLEtBQUtpRixLQUFLLEVBQUU7Z0JBQ3RFYSxZQUFZLEdBQTBCLE9BQXZCQyxPQUFPQyxRQUFRLENBQUNDLE1BQU0sRUFBQztZQUN4QztZQUVBLElBQUk3RixPQUFPLE1BQU1BO1lBRWpCK0UsTUFBTWhGLE9BQU8sQ0FBQyw4QkFBOEI7WUFDNUNnQixrQkFBa0I7UUFDcEIsRUFBRSxPQUFPZixPQUFZO1lBQ25CZ0UsUUFBUWhFLEtBQUssQ0FBQyx5QkFBeUJBO1lBQ3ZDK0UsTUFBTS9FLEtBQUssQ0FBQyw4QkFBOEJBLE1BQU1rRixPQUFPLElBQUk7UUFDN0QsU0FBVTtZQUNSakUsa0JBQWtCO1FBQ3BCO0lBQ0Y7SUFFQSxNQUFNNkUsb0JBQW9CO1FBQ3hCLElBQUksQ0FBQ3hFLGdCQUFnQkUsUUFBUSxDQUFDaUQsSUFBSSxJQUFJO1lBQ3BDTSxNQUFNL0UsS0FBSyxDQUFDO1lBQ1o7UUFDRjtRQUVBLElBQUksQ0FBQ3NCLGdCQUFnQkcsZUFBZSxDQUFDZ0QsSUFBSSxJQUFJO1lBQzNDTSxNQUFNL0UsS0FBSyxDQUFDO1lBQ1o7UUFDRjtRQUVBLElBQUlzQixnQkFBZ0JFLFFBQVEsTUFBSzVCLGlCQUFBQSwyQkFBQUEsS0FBTWlGLEtBQUssR0FBRTtZQUM1Q0UsTUFBTS9FLEtBQUssQ0FBQztZQUNaO1FBQ0Y7UUFFQSx5QkFBeUI7UUFDekIsTUFBTStGLGFBQWE7UUFDbkIsSUFBSSxDQUFDQSxXQUFXQyxJQUFJLENBQUMxRSxnQkFBZ0JFLFFBQVEsR0FBRztZQUM5Q3VELE1BQU0vRSxLQUFLLENBQUM7WUFDWjtRQUNGO1FBRUFxQixrQkFBa0I7UUFDbEIsSUFBSTtZQUNGLGdDQUFnQztZQUNoQyxNQUFNLEVBQUVyQixPQUFPMEUsV0FBVyxFQUFFLEdBQUcsTUFBTTVFLFNBQVM2RSxJQUFJLENBQUNDLGtCQUFrQixDQUFDO2dCQUNwRUMsT0FBT2pGLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTWlGLEtBQUssS0FBSTtnQkFDdEJDLFVBQVV4RCxnQkFBZ0JHLGVBQWU7WUFDM0M7WUFFQSxJQUFJaUQsYUFBYTtnQkFDZkssTUFBTS9FLEtBQUssQ0FBQztnQkFDWnFCLGtCQUFrQjtnQkFDbEI7WUFDRjtZQUVBLGVBQWU7WUFDZixNQUFNLEVBQUVyQixPQUFPZ0YsV0FBVyxFQUFFLEdBQUcsTUFBTWxGLFNBQVM2RSxJQUFJLENBQUNNLFVBQVUsQ0FBQztnQkFDNURKLE9BQU92RCxnQkFBZ0JFLFFBQVE7WUFDakM7WUFFQSxJQUFJd0QsYUFBYSxNQUFNQTtZQUV2QkQsTUFBTWhGLE9BQU8sQ0FBQywyQkFBMkI7WUFDekNvQix3QkFBd0I7WUFDeEJJLG1CQUFtQjtnQkFBRUMsVUFBVTtnQkFBSUMsaUJBQWlCO1lBQUc7UUFDekQsRUFBRSxPQUFPekIsT0FBWTtZQUNuQmdFLFFBQVFoRSxLQUFLLENBQUMsdUJBQXVCQTtZQUNyQytFLE1BQU0vRSxLQUFLLENBQUMsMEJBQTBCQSxNQUFNa0YsT0FBTyxJQUFJO1FBQ3pELFNBQVU7WUFDUjdELGtCQUFrQjtRQUNwQjtJQUNGO0lBRUEsTUFBTTRFLHdCQUF3QjtRQUM1QixJQUFJLENBQUNyRyxNQUFNO1FBRVhTLFdBQVc7UUFDWCxJQUFJO1lBQ0YsZ0NBQWdDO1lBQ2hDLE1BQU02RixXQUFXLE1BQU1sRCxNQUFNLDRCQUE0QjtnQkFDdkRtRCxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7WUFDRjtZQUVBLElBQUksQ0FBQ0YsU0FBU2hELEVBQUUsRUFBRTtnQkFDaEIsTUFBTW1ELFlBQVksTUFBTUgsU0FBUy9DLElBQUk7Z0JBQ3JDLE1BQU0sSUFBSW1ELE1BQU1ELFVBQVVyRyxLQUFLLElBQUk7WUFDckM7WUFFQStFLE1BQU1oRixPQUFPLENBQUM7WUFFZCx3QkFBd0I7WUFDeEIsTUFBTUQsU0FBUzZFLElBQUksQ0FBQzRCLE9BQU87WUFDM0JDLFdBQVc7Z0JBQ1RiLE9BQU9DLFFBQVEsQ0FBQ2EsSUFBSSxHQUFHO1lBQ3pCLEdBQUc7UUFFTCxFQUFFLE9BQU96RyxPQUFZO1lBQ25CZ0UsUUFBUWhFLEtBQUssQ0FBQywyQkFBMkJBO1lBQ3pDK0UsTUFBTS9FLEtBQUssQ0FBQ0EsTUFBTWtGLE9BQU8sSUFBSTtRQUMvQixTQUFVO1lBQ1I3RSxXQUFXO1FBQ2I7SUFDRjtJQUVBLHFCQUNFLDhEQUFDcUc7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDs7c0NBQ0MsOERBQUNFOzRCQUFHRCxXQUFVO3NDQUFtQzs7Ozs7O3NDQUNqRCw4REFBQ0U7NEJBQUVGLFdBQVU7c0NBQXFCOzs7Ozs7Ozs7Ozs7Ozs7OzswQkFLdEMsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRztvQkFBSUgsV0FBVTs4QkFDWmxFLGFBQWFzRSxHQUFHLENBQUMsQ0FBQ0M7d0JBQ2pCLE1BQU1DLE9BQU9ELEtBQUtwRSxJQUFJO3dCQUN0QixxQkFDRSw4REFBQ3NFOzRCQUVDQyxTQUFTLElBQU1oSCxpQkFBaUI2RyxLQUFLdEUsRUFBRTs0QkFDdkNpRSxXQUFXLHNGQUlWLE9BSEN6RyxrQkFBa0I4RyxLQUFLdEUsRUFBRSxHQUNyQixzQ0FDQTs7OENBR04sOERBQUN1RTtvQ0FBS04sV0FBVTs7Ozs7O2dDQUNmSyxLQUFLckUsS0FBSzs7MkJBVE5xRSxLQUFLdEUsRUFBRTs7Ozs7b0JBWWxCOzs7Ozs7Ozs7OzswQkFLSiw4REFBQ2dFO2dCQUFJQyxXQUFVOztvQkFDUnpHLGtCQUFrQiwyQkFDakIsOERBQUN3Rzt3QkFBSUMsV0FBVTs7MENBRWIsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ1M7d0NBQUdULFdBQVU7a0RBQTJDOzs7Ozs7a0RBQ3pELDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEOztrRUFDQyw4REFBQ0c7d0RBQUVGLFdBQVU7OzREQUFnQjswRUFBc0IsOERBQUNVO2dFQUFLVixXQUFVOzBFQUE2Qi9HLGlCQUFBQSwyQkFBQUEsS0FBTWlGLEtBQUs7Ozs7Ozs7Ozs7OztrRUFDM0csOERBQUNnQzt3REFBRUYsV0FBVTtrRUFBNkI7Ozs7Ozs7Ozs7OzswREFFNUMsOERBQUNPO2dEQUNDUCxXQUFVO2dEQUNWUSxTQUFTLElBQU1oRyx3QkFBd0I7MERBQ3hDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBT0wsOERBQUN1RjtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNTO3dDQUFHVCxXQUFVO2tEQUEyQzs7Ozs7O2tEQUN6RCw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEOzswRUFDQyw4REFBQ1k7Z0VBQUdYLFdBQVU7MEVBQXlDOzs7Ozs7MEVBQ3ZELDhEQUFDRTtnRUFBRUYsV0FBVTswRUFDVi9HLENBQUFBLGlCQUFBQSw0QkFBQUEsc0JBQUFBLEtBQU0ySCxhQUFhLGNBQW5CM0gsMENBQUFBLG9CQUFxQjRILFNBQVMsS0FBSTVILENBQUFBLGlCQUFBQSw0QkFBQUEsdUJBQUFBLEtBQU0ySCxhQUFhLGNBQW5CM0gsMkNBQUFBLHFCQUFxQjZILFVBQVUsSUFBRyxPQUFNN0gsaUJBQUFBLDRCQUFBQSx1QkFBQUEsS0FBTTJILGFBQWEsY0FBbkIzSCwyQ0FBQUEscUJBQXFCOEgsU0FBUyxLQUFJOzs7Ozs7Ozs7Ozs7a0VBR2pILDhEQUFDaEI7OzBFQUNDLDhEQUFDWTtnRUFBR1gsV0FBVTswRUFBeUM7Ozs7OzswRUFDdkQsOERBQUNFO2dFQUFFRixXQUFVOzBFQUNWL0csQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNK0gsVUFBVSxJQUFHLElBQUlDLEtBQUtoSSxLQUFLK0gsVUFBVSxFQUFFRSxrQkFBa0IsQ0FBQyxTQUFTO29FQUN4RUMsTUFBTTtvRUFDTkMsT0FBTztvRUFDUEMsS0FBSztnRUFDUCxLQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBSVgsOERBQUN0QjtnREFBSUMsV0FBVTswREFDYiw0RUFBQ0Q7O3NFQUNDLDhEQUFDWTs0REFBR1gsV0FBVTtzRUFBeUM7Ozs7OztzRUFDdkQsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0U7O3dFQUFHekUsVUFBVUUsV0FBVzt3RUFBQzs7Ozs7Ozs4RUFDMUIsOERBQUN1RTs7d0VBQUd6RSxVQUFVRyxXQUFXO3dFQUFDOzs7Ozs7OzhFQUMxQiw4REFBQ3NFOzt3RUFBR3pFLFVBQVVJLGVBQWU7d0VBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FReEMsOERBQUNrRTtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNTO3dDQUFHVCxXQUFVO2tEQUEyQzs7Ozs7O2tEQUN6RCw4REFBQ3NCO3dDQUFLQyxVQUFVNUQ7d0NBQXNCcUMsV0FBVTs7MERBQzlDLDhEQUFDRDs7a0VBQ0MsOERBQUNuSCx1REFBS0E7d0RBQUM0SSxTQUFRO3dEQUFrQnhCLFdBQVU7a0VBQW9DOzs7Ozs7a0VBQy9FLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUN5QjtnRUFDQzFGLElBQUc7Z0VBQ0gyRixNQUFNL0gsc0JBQXNCLFNBQVM7Z0VBQ3JDZ0ksT0FBTzVHLGFBQWFELGVBQWU7Z0VBQ25DOEcsVUFBVSxDQUFDaEUsSUFBTTVDLGdCQUFnQjZHLENBQUFBLE9BQVM7NEVBQUUsR0FBR0EsSUFBSTs0RUFBRS9HLGlCQUFpQjhDLEVBQUVrRSxNQUFNLENBQUNILEtBQUs7d0VBQUM7Z0VBQ3JGSSxhQUFZO2dFQUNaL0IsV0FBVTtnRUFDVmdDLFFBQVE7Ozs7OzswRUFFViw4REFBQ3pCO2dFQUNDbUIsTUFBSztnRUFDTGxCLFNBQVMsSUFBTTVHLHVCQUF1QixDQUFDRDtnRUFDdkNxRyxXQUFVOzBFQUVUckcsb0NBQXNCLDhEQUFDbkIsd0tBQVlBO29FQUFDd0gsV0FBVTs7Ozs7eUZBQWUsOERBQUN6SCx3S0FBT0E7b0VBQUN5SCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFJdkYsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7OzBFQUNDLDhEQUFDbkgsdURBQUtBO2dFQUFDNEksU0FBUTtnRUFBY3hCLFdBQVU7MEVBQW9DOzs7Ozs7MEVBQzNFLDhEQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUN5Qjt3RUFDQzFGLElBQUc7d0VBQ0gyRixNQUFNN0gsa0JBQWtCLFNBQVM7d0VBQ2pDOEgsT0FBTzVHLGFBQWFFLFdBQVc7d0VBQy9CMkcsVUFBVSxDQUFDaEUsSUFBTTVDLGdCQUFnQjZHLENBQUFBLE9BQVM7b0ZBQUUsR0FBR0EsSUFBSTtvRkFBRTVHLGFBQWEyQyxFQUFFa0UsTUFBTSxDQUFDSCxLQUFLO2dGQUFDO3dFQUNqRkksYUFBWTt3RUFDWi9CLFdBQVU7d0VBQ1ZnQyxRQUFROzs7Ozs7a0ZBRVYsOERBQUN6Qjt3RUFDQ21CLE1BQUs7d0VBQ0xsQixTQUFTLElBQU0xRyxtQkFBbUIsQ0FBQ0Q7d0VBQ25DbUcsV0FBVTtrRkFFVG5HLGdDQUFrQiw4REFBQ3JCLHdLQUFZQTs0RUFBQ3dILFdBQVU7Ozs7O2lHQUFlLDhEQUFDekgsd0tBQU9BOzRFQUFDeUgsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBSW5GLDhEQUFDRDs7MEVBQ0MsOERBQUNuSCx1REFBS0E7Z0VBQUM0SSxTQUFRO2dFQUFrQnhCLFdBQVU7MEVBQW9DOzs7Ozs7MEVBQy9FLDhEQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUN5Qjt3RUFDQzFGLElBQUc7d0VBQ0gyRixNQUFNM0gsc0JBQXNCLFNBQVM7d0VBQ3JDNEgsT0FBTzVHLGFBQWFHLGVBQWU7d0VBQ25DMEcsVUFBVSxDQUFDaEUsSUFBTTVDLGdCQUFnQjZHLENBQUFBLE9BQVM7b0ZBQUUsR0FBR0EsSUFBSTtvRkFBRTNHLGlCQUFpQjBDLEVBQUVrRSxNQUFNLENBQUNILEtBQUs7Z0ZBQUM7d0VBQ3JGSSxhQUFZO3dFQUNaL0IsV0FBVTt3RUFDVmdDLFFBQVE7Ozs7OztrRkFFViw4REFBQ3pCO3dFQUNDbUIsTUFBSzt3RUFDTGxCLFNBQVMsSUFBTXhHLHVCQUF1QixDQUFDRDt3RUFDdkNpRyxXQUFVO2tGQUVUakcsb0NBQXNCLDhEQUFDdkIsd0tBQVlBOzRFQUFDd0gsV0FBVTs7Ozs7aUdBQWUsOERBQUN6SCx3S0FBT0E7NEVBQUN5SCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFLekYsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7OzREQUF3QjswRUFDQyw4REFBQ087Z0VBQ3JDbUIsTUFBSztnRUFDTDFCLFdBQVU7Z0VBQ1ZRLFNBQVMsSUFBTXBHLGtCQUFrQjswRUFDbEM7Ozs7Ozs7Ozs7OztrRUFJSCw4REFBQ3pCLHlEQUFNQTt3REFBQytJLE1BQUs7d0RBQVNPLFVBQVV4STt3REFBU3VHLFdBQVU7a0VBQ2hEdkcsVUFBVSxjQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBUXBDRixrQkFBa0IsaUNBQ2pCLDhEQUFDd0c7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ1M7b0NBQUdULFdBQVU7OENBQTJDOzs7Ozs7OENBQ3pELDhEQUFDRDtvQ0FBSUMsV0FBVTs7d0NBQ1prQyxPQUFPQyxPQUFPLENBQUNoSCxzQkFBc0JpRixHQUFHLENBQUM7Z0RBQUMsQ0FBQ2dDLEtBQUtULE1BQU07aUVBQ3JELDhEQUFDNUI7Z0RBQWNDLFdBQVU7O2tFQUN2Qiw4REFBQ0Q7OzBFQUNDLDhEQUFDWTtnRUFBR1gsV0FBVTs7b0VBQ1hvQyxRQUFRLHdCQUF3QjtvRUFDaENBLFFBQVEsb0JBQW9CO29FQUM1QkEsUUFBUSxpQkFBaUI7b0VBQ3pCQSxRQUFRLHFCQUFxQjs7Ozs7OzswRUFFaEMsOERBQUNsQztnRUFBRUYsV0FBVTs7b0VBQ1ZvQyxRQUFRLHdCQUF3QjtvRUFDaENBLFFBQVEsb0JBQW9CO29FQUM1QkEsUUFBUSxpQkFBaUI7b0VBQ3pCQSxRQUFRLHFCQUFxQjs7Ozs7Ozs7Ozs7OztrRUFHbEMsOERBQUNwRzt3REFBTWdFLFdBQVU7OzBFQUNmLDhEQUFDeUI7Z0VBQ0NDLE1BQUs7Z0VBQ0xXLFNBQVNWO2dFQUNUQyxVQUFVLENBQUNoRSxJQUFNeEMsd0JBQXdCeUcsQ0FBQUEsT0FBUzs0RUFBRSxHQUFHQSxJQUFJOzRFQUFFLENBQUNPLElBQUksRUFBRXhFLEVBQUVrRSxNQUFNLENBQUNPLE9BQU87d0VBQUM7Z0VBQ3JGckMsV0FBVTs7Ozs7OzBFQUVaLDhEQUFDRDtnRUFBSUMsV0FBVTs7Ozs7Ozs7Ozs7OzsrQ0F0QlRvQzs7Ozs7O3NEQTBCWiw4REFBQ3JDOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDckgseURBQU1BO2dEQUFDNkgsU0FBU2hDO2dEQUEwQnlELFVBQVV4STtnREFBU3VHLFdBQVU7MERBQ3JFdkcsVUFBVSxjQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQVFwQ0Ysa0JBQWtCLDRCQUNqQiw4REFBQ3dHO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ1M7NENBQUdULFdBQVU7c0RBQXNDOzs7Ozs7c0RBQ3BELDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUMzSCx3S0FBZUE7b0RBQUMySCxXQUFVOzs7Ozs7Z0RBQVk7Ozs7Ozs7Ozs7Ozs7OENBSzNDLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDRDs7a0VBQ0MsOERBQUNZO3dEQUFHWCxXQUFVO2tFQUF5Qzs7Ozs7O2tFQUN2RCw4REFBQ0U7d0RBQUVGLFdBQVU7a0VBQTZCOzs7Ozs7a0VBQzFDLDhEQUFDckgseURBQU1BO3dEQUNMMkosU0FBUTt3REFDUkMsTUFBSzt3REFDTC9CLFNBQVMsSUFBTWhILGlCQUFpQjt3REFDaEN3RyxXQUFVO2tFQUNYOzs7Ozs7Ozs7Ozs7Ozs7OztzREFLTCw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDs7c0VBQ0MsOERBQUNZOzREQUFHWCxXQUFVO3NFQUF5Qzs7Ozs7O3NFQUN2RCw4REFBQ0U7NERBQUVGLFdBQVU7O2dFQUNWdkUsVUFBVUUsV0FBVyxHQUFHRixVQUFVRyxXQUFXLEdBQUdILFVBQVVJLGVBQWU7Z0VBQUM7Ozs7Ozs7Ozs7Ozs7OERBRy9FLDhEQUFDa0U7O3NFQUNDLDhEQUFDWTs0REFBR1gsV0FBVTtzRUFBeUM7Ozs7OztzRUFDdkQsOERBQUNFOzREQUFFRixXQUFVO3NFQUNWL0csQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNK0gsVUFBVSxJQUFHLElBQUlDLEtBQUtoSSxLQUFLK0gsVUFBVSxFQUFFRSxrQkFBa0IsQ0FBQyxTQUFTO2dFQUN4RUMsTUFBTTtnRUFDTkMsT0FBTztnRUFDUEMsS0FBSzs0REFDUCxLQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQkFTbEI5SCxrQkFBa0IsMEJBQ2pCLDhEQUFDd0c7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDdkgsd0tBQVNBOzRDQUFDdUgsV0FBVTs7Ozs7O3NEQUNyQiw4REFBQ1M7NENBQUdULFdBQVU7c0RBQXFDOzs7Ozs7Ozs7Ozs7OENBR3JELDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNXOzRDQUFHWCxXQUFVO3NEQUF3Qzs7Ozs7O3NEQUN0RCw4REFBQ0U7NENBQUVGLFdBQVU7c0RBQTRCOzs7Ozs7c0RBR3pDLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUN3QztvREFBR3hDLFdBQVU7OERBQXdDOzs7Ozs7OERBQ3RELDhEQUFDeUM7b0RBQUd6QyxXQUFVOztzRUFDWiw4REFBQzBDOztnRUFBRztnRUFBR2pILFVBQVVFLFdBQVc7Z0VBQUM7Ozs7Ozs7c0VBQzdCLDhEQUFDK0c7O2dFQUFHO2dFQUFHakgsVUFBVUcsV0FBVztnRUFBQzs7Ozs7OztzRUFDN0IsOERBQUM4Rzs7Z0VBQUc7Z0VBQUdqSCxVQUFVSSxlQUFlO2dFQUFDOzs7Ozs7O3NFQUNqQyw4REFBQzZHO3NFQUFHOzs7Ozs7c0VBQ0osOERBQUNBO3NFQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBR1IsOERBQUMvSix5REFBTUE7NENBQ0wySixTQUFROzRDQUNSdEMsV0FBVTs0Q0FDVlEsU0FBUyxJQUFNdEcsbUJBQW1COzRDQUNsQytILFVBQVV4STtzREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFVZGMsc0NBQ0MsOERBQUN3RjtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDdEgsd0tBQVlBO3dDQUFDc0gsV0FBVTs7Ozs7Ozs7Ozs7OENBRTFCLDhEQUFDUztvQ0FBR1QsV0FBVTs4Q0FBc0M7Ozs7Ozs7Ozs7OztzQ0FHdEQsOERBQUNFOzRCQUFFRixXQUFVO3NDQUFxQjs7Ozs7O3NDQUtsQyw4REFBQ3NCOzRCQUFLQyxVQUFVLENBQUMzRDtnQ0FBUUEsRUFBRUMsY0FBYztnQ0FBSXNCOzRCQUFxQjs0QkFBR2EsV0FBVTs7OENBQzdFLDhEQUFDRDs7c0RBQ0MsOERBQUMvRDs0Q0FBTXdGLFNBQVE7NENBQVd4QixXQUFVO3NEQUErQzs7Ozs7O3NEQUduRiw4REFBQ3lCOzRDQUNDMUYsSUFBRzs0Q0FDSDJGLE1BQUs7NENBQ0xDLE9BQU9oSCxnQkFBZ0JFLFFBQVE7NENBQy9CK0csVUFBVSxDQUFDaEUsSUFBTWhELG1CQUFtQmlILENBQUFBLE9BQVM7d0RBQUUsR0FBR0EsSUFBSTt3REFBRWhILFVBQVUrQyxFQUFFa0UsTUFBTSxDQUFDSCxLQUFLO29EQUFDOzRDQUNqRkksYUFBWTs0Q0FDWi9CLFdBQVU7NENBQ1ZnQyxRQUFROzs7Ozs7Ozs7Ozs7OENBSVosOERBQUNqQzs7c0RBQ0MsOERBQUMvRDs0Q0FBTXdGLFNBQVE7NENBQXVCeEIsV0FBVTtzREFBK0M7Ozs7OztzREFHL0YsOERBQUN5Qjs0Q0FDQzFGLElBQUc7NENBQ0gyRixNQUFLOzRDQUNMQyxPQUFPaEgsZ0JBQWdCRyxlQUFlOzRDQUN0QzhHLFVBQVUsQ0FBQ2hFLElBQU1oRCxtQkFBbUJpSCxDQUFBQSxPQUFTO3dEQUFFLEdBQUdBLElBQUk7d0RBQUUvRyxpQkFBaUI4QyxFQUFFa0UsTUFBTSxDQUFDSCxLQUFLO29EQUFDOzRDQUN4RkksYUFBWTs0Q0FDWi9CLFdBQVU7NENBQ1ZnQyxRQUFROzs7Ozs7Ozs7Ozs7OENBSVosOERBQUNqQztvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNXOzRDQUFHWCxXQUFVO3NEQUEyQzs7Ozs7O3NEQUN6RCw4REFBQ3lDOzRDQUFHekMsV0FBVTs7OERBQ1osOERBQUMwQzs4REFBRzs7Ozs7OzhEQUNKLDhEQUFDQTs4REFBRzs7Ozs7OzhEQUNKLDhEQUFDQTs4REFBRzs7Ozs7OzhEQUNKLDhEQUFDQTs4REFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUlSLDhEQUFDM0M7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDckgseURBQU1BOzRDQUNMK0ksTUFBSzs0Q0FDTFksU0FBUTs0Q0FDUjlCLFNBQVM7Z0RBQ1BoRyx3QkFBd0I7Z0RBQ3hCSSxtQkFBbUI7b0RBQUVDLFVBQVU7b0RBQUlDLGlCQUFpQjtnREFBRzs0Q0FDekQ7NENBQ0FrRixXQUFVOzRDQUNWaUMsVUFBVXhIO3NEQUNYOzs7Ozs7c0RBR0QsOERBQUM5Qix5REFBTUE7NENBQ0wrSSxNQUFLOzRDQUNMTyxVQUFVeEg7NENBQ1Z1RixXQUFVO3NEQUVUdkYsK0JBQ0MsOERBQUNzRjtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEO3dEQUFJQyxXQUFVOzs7Ozs7b0RBQTBGOzs7Ozs7dURBSTNHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVViN0YsZ0NBQ0MsOERBQUM0RjtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDdEgsd0tBQVlBO3dDQUFDc0gsV0FBVTs7Ozs7Ozs7Ozs7OENBRTFCLDhEQUFDUztvQ0FBR1QsV0FBVTs4Q0FBc0M7Ozs7Ozs7Ozs7OztzQ0FHdEQsOERBQUNFOzRCQUFFRixXQUFVOztnQ0FBcUI7OENBQ0ksOERBQUNVO29DQUFLVixXQUFVOzhDQUFlL0csaUJBQUFBLDJCQUFBQSxLQUFNaUYsS0FBSzs7Ozs7O2dDQUFROzs7Ozs7O3NDQUl4Riw4REFBQzZCOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ1c7b0NBQUdYLFdBQVU7OENBQXlDOzs7Ozs7OENBQ3ZELDhEQUFDeUM7b0NBQUd6QyxXQUFVOztzREFDWiw4REFBQzBDO3NEQUFHOzs7Ozs7c0RBQ0osOERBQUNBO3NEQUFHOzs7Ozs7c0RBQ0osOERBQUNBO3NEQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSVIsOERBQUMzQzs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNySCx5REFBTUE7b0NBQ0wySixTQUFRO29DQUNSOUIsU0FBUyxJQUFNcEcsa0JBQWtCO29DQUNqQzRGLFdBQVU7b0NBQ1ZpQyxVQUFVNUg7OENBQ1g7Ozs7Ozs4Q0FHRCw4REFBQzFCLHlEQUFNQTtvQ0FDTDZILFNBQVMzQjtvQ0FDVG9ELFVBQVU1SDtvQ0FDVjJGLFdBQVU7OENBRVQzRiwrQkFDQyw4REFBQzBGO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7Ozs7Ozs0Q0FBMEY7Ozs7OzsrQ0FJM0c7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBU1gvRixpQ0FDQyw4REFBQzhGO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ3ZILHdLQUFTQTtvQ0FBQ3VILFdBQVU7Ozs7Ozs4Q0FDckIsOERBQUNTO29DQUFHVCxXQUFVOzhDQUFzQzs7Ozs7Ozs7Ozs7O3NDQUd0RCw4REFBQ0U7NEJBQUVGLFdBQVU7c0NBQXFCOzs7Ozs7c0NBSWxDLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNXO29DQUFHWCxXQUFVOzhDQUF3Qzs7Ozs7OzhDQUN0RCw4REFBQ3lDO29DQUFHekMsV0FBVTs7c0RBQ1osOERBQUMwQzs7Z0RBQUc7Z0RBQUdqSCxVQUFVRSxXQUFXO2dEQUFDOzs7Ozs7O3NEQUM3Qiw4REFBQytHOztnREFBRztnREFBR2pILFVBQVVHLFdBQVc7Z0RBQUM7Ozs7Ozs7c0RBQzdCLDhEQUFDOEc7O2dEQUFHO2dEQUFHakgsVUFBVUksZUFBZTtnREFBQzs7Ozs7OztzREFDakMsOERBQUM2RztzREFBRzs7Ozs7O3NEQUNKLDhEQUFDQTtzREFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUlSLDhEQUFDM0M7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDckgseURBQU1BO29DQUNMMkosU0FBUTtvQ0FDUjlCLFNBQVMsSUFBTXRHLG1CQUFtQjtvQ0FDbEM4RixXQUFVO29DQUNWaUMsVUFBVXhJOzhDQUNYOzs7Ozs7OENBR0QsOERBQUNkLHlEQUFNQTtvQ0FDTDZILFNBQVNsQjtvQ0FDVDJDLFVBQVV4STtvQ0FDVnVHLFdBQVU7OENBRVR2RyxVQUFVLGdCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRM0M7R0E3eEJ3QlQ7O1FBQ1BiLHNEQUFTQTtRQUNQWSxtRUFBZUE7UUFFT0YsMERBQVFBOzs7S0FKekJHIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcYXBwXFxzZXR0aW5nc1xccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7XG4gIFVzZXJJY29uLFxuICBLZXlJY29uLFxuICBTaGllbGRDaGVja0ljb24sXG4gIEJlbGxJY29uLFxuICBFeWVJY29uLFxuICBFeWVTbGFzaEljb24sXG4gIFRyYXNoSWNvbixcbiAgRW52ZWxvcGVJY29uXG59IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZSc7XG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvQnV0dG9uJztcbmltcG9ydCB7IExhYmVsIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2xhYmVsJztcbmltcG9ydCB7IHVzZVRvYXN0IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL1RvYXN0JztcbmltcG9ydCB7IGNyZWF0ZVN1cGFiYXNlQnJvd3NlckNsaWVudCB9IGZyb20gJ0AvbGliL3N1cGFiYXNlL2NsaWVudCc7XG5pbXBvcnQgeyB1c2VTdWJzY3JpcHRpb24gfSBmcm9tICdAL2hvb2tzL3VzZVN1YnNjcmlwdGlvbic7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFNldHRpbmdzUGFnZSgpIHtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG4gIGNvbnN0IHsgdXNlciB9ID0gdXNlU3Vic2NyaXB0aW9uKCk7XG4gIGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlU3VwYWJhc2VCcm93c2VyQ2xpZW50KCk7XG4gIGNvbnN0IHsgc3VjY2VzcywgZXJyb3I6IHRvYXN0RXJyb3IgfSA9IHVzZVRvYXN0KCk7XG5cbiAgY29uc3QgW2FjdGl2ZVNlY3Rpb24sIHNldEFjdGl2ZVNlY3Rpb25dID0gdXNlU3RhdGUoJ2FjY291bnQnKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2hvd0N1cnJlbnRQYXNzd29yZCwgc2V0U2hvd0N1cnJlbnRQYXNzd29yZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzaG93TmV3UGFzc3dvcmQsIHNldFNob3dOZXdQYXNzd29yZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzaG93Q29uZmlybVBhc3N3b3JkLCBzZXRTaG93Q29uZmlybVBhc3N3b3JkXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Nob3dEZWxldGVNb2RhbCwgc2V0U2hvd0RlbGV0ZU1vZGFsXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Nob3dSZXNldE1vZGFsLCBzZXRTaG93UmVzZXRNb2RhbF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtpc1Jlc2V0TG9hZGluZywgc2V0SXNSZXNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2hvd0NoYW5nZUVtYWlsTW9kYWwsIHNldFNob3dDaGFuZ2VFbWFpbE1vZGFsXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzRW1haWxMb2FkaW5nLCBzZXRJc0VtYWlsTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtlbWFpbENoYW5nZURhdGEsIHNldEVtYWlsQ2hhbmdlRGF0YV0gPSB1c2VTdGF0ZSh7XG4gICAgbmV3RW1haWw6ICcnLFxuICAgIGN1cnJlbnRQYXNzd29yZDogJydcbiAgfSk7XG5cbiAgY29uc3QgW3Bhc3N3b3JkRGF0YSwgc2V0UGFzc3dvcmREYXRhXSA9IHVzZVN0YXRlKHtcbiAgICBjdXJyZW50UGFzc3dvcmQ6ICcnLFxuICAgIG5ld1Bhc3N3b3JkOiAnJyxcbiAgICBjb25maXJtUGFzc3dvcmQ6ICcnXG4gIH0pO1xuXG4gIGNvbnN0IFtub3RpZmljYXRpb25TZXR0aW5ncywgc2V0Tm90aWZpY2F0aW9uU2V0dGluZ3NdID0gdXNlU3RhdGUoe1xuICAgIGVtYWlsTm90aWZpY2F0aW9uczogdHJ1ZSxcbiAgICBzZWN1cml0eUFsZXJ0czogdHJ1ZSxcbiAgICB1c2FnZUFsZXJ0czogdHJ1ZSxcbiAgICBtYXJrZXRpbmdFbWFpbHM6IGZhbHNlXG4gIH0pO1xuXG4gIGNvbnN0IFt1c2VyU3RhdHMsIHNldFVzZXJTdGF0c10gPSB1c2VTdGF0ZSh7XG4gICAgY29uZmlnQ291bnQ6IDAsXG4gICAgYXBpS2V5Q291bnQ6IDAsXG4gICAgdXNlckFwaUtleUNvdW50OiAwXG4gIH0pO1xuXG4gIGNvbnN0IHNpZGViYXJJdGVtcyA9IFtcbiAgICB7IGlkOiAnYWNjb3VudCcsIGxhYmVsOiAnQWNjb3VudCBzZXR0aW5ncycsIGljb246IFVzZXJJY29uIH0sXG4gICAgeyBpZDogJ25vdGlmaWNhdGlvbnMnLCBsYWJlbDogJ05vdGlmaWNhdGlvbnMnLCBpY29uOiBCZWxsSWNvbiB9LFxuICAgIHsgaWQ6ICdzZWN1cml0eScsIGxhYmVsOiAnU2VjdXJpdHknLCBpY29uOiBTaGllbGRDaGVja0ljb24gfSxcbiAgICB7IGlkOiAnZGFuZ2VyJywgbGFiZWw6ICdEYW5nZXIgem9uZScsIGljb246IFRyYXNoSWNvbiB9XG4gIF07XG5cbiAgLy8gTG9hZCB1c2VyIGRhdGEgYW5kIHN0YXRzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHVzZXIpIHtcbiAgICAgIGxvYWRVc2VyU3RhdHMoKTtcbiAgICAgIGxvYWROb3RpZmljYXRpb25TZXR0aW5ncygpO1xuICAgIH1cbiAgfSwgW3VzZXJdKTtcblxuICBjb25zdCBsb2FkVXNlclN0YXRzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBHZXQgY29uZmlndXJhdGlvbiBjb3VudFxuICAgICAgY29uc3QgY29uZmlnUmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9jdXN0b20tY29uZmlncycpO1xuICAgICAgY29uc3QgY29uZmlnRGF0YSA9IGNvbmZpZ1Jlc3BvbnNlLm9rID8gYXdhaXQgY29uZmlnUmVzcG9uc2UuanNvbigpIDogW107XG4gICAgICBjb25zdCBjb25maWdzID0gQXJyYXkuaXNBcnJheShjb25maWdEYXRhKSA/IGNvbmZpZ0RhdGEgOiBbXTtcblxuICAgICAgLy8gR2V0IHVzZXIgQVBJIGtleXMgY291bnRcbiAgICAgIGNvbnN0IHVzZXJLZXlzUmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS91c2VyLWFwaS1rZXlzJyk7XG4gICAgICBjb25zdCB1c2VyS2V5c0RhdGEgPSB1c2VyS2V5c1Jlc3BvbnNlLm9rID8gYXdhaXQgdXNlcktleXNSZXNwb25zZS5qc29uKCkgOiB7IGFwaV9rZXlzOiBbXSB9O1xuICAgICAgY29uc3QgdXNlcktleXMgPSB1c2VyS2V5c0RhdGEuYXBpX2tleXMgfHwgW107XG5cbiAgICAgIC8vIEdldCB0b3RhbCBBUEkga2V5cyBjb3VudCBhY3Jvc3MgYWxsIGNvbmZpZ3NcbiAgICAgIGxldCB0b3RhbEFwaUtleXMgPSAwO1xuICAgICAgZm9yIChjb25zdCBjb25maWcgb2YgY29uZmlncykge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IGtleXNSZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL2N1c3RvbS1jb25maWdzLyR7Y29uZmlnLmlkfS9rZXlzYCk7XG4gICAgICAgICAgY29uc3Qga2V5c0RhdGEgPSBrZXlzUmVzcG9uc2Uub2sgPyBhd2FpdCBrZXlzUmVzcG9uc2UuanNvbigpIDogeyBhcGlfa2V5czogW10gfTtcbiAgICAgICAgICB0b3RhbEFwaUtleXMgKz0gKGtleXNEYXRhLmFwaV9rZXlzIHx8IFtdKS5sZW5ndGg7XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyBrZXlzIGZvciBjb25maWc6JywgY29uZmlnLmlkLCBlcnJvcik7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgc2V0VXNlclN0YXRzKHtcbiAgICAgICAgY29uZmlnQ291bnQ6IGNvbmZpZ3MubGVuZ3RoLFxuICAgICAgICBhcGlLZXlDb3VudDogdG90YWxBcGlLZXlzLFxuICAgICAgICB1c2VyQXBpS2V5Q291bnQ6IHVzZXJLZXlzLmxlbmd0aFxuICAgICAgfSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgdXNlciBzdGF0czonLCBlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGxvYWROb3RpZmljYXRpb25TZXR0aW5ncyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgLy8gTG9hZCBmcm9tIHVzZXIgbWV0YWRhdGEgb3IgbG9jYWwgc3RvcmFnZVxuICAgICAgY29uc3Qgc2F2ZWRTZXR0aW5ncyA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdub3RpZmljYXRpb25fc2V0dGluZ3MnKTtcbiAgICAgIGlmIChzYXZlZFNldHRpbmdzKSB7XG4gICAgICAgIHNldE5vdGlmaWNhdGlvblNldHRpbmdzKEpTT04ucGFyc2Uoc2F2ZWRTZXR0aW5ncykpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIG5vdGlmaWNhdGlvbiBzZXR0aW5nczonLCBlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVBhc3N3b3JkQ2hhbmdlID0gYXN5bmMgKGU6IFJlYWN0LkZvcm1FdmVudCkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcblxuICAgIGlmICghcGFzc3dvcmREYXRhLmN1cnJlbnRQYXNzd29yZC50cmltKCkpIHtcbiAgICAgIHRvYXN0RXJyb3IoJ0N1cnJlbnQgcGFzc3dvcmQgaXMgcmVxdWlyZWQnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBpZiAoIXBhc3N3b3JkRGF0YS5uZXdQYXNzd29yZC50cmltKCkpIHtcbiAgICAgIHRvYXN0RXJyb3IoJ05ldyBwYXNzd29yZCBpcyByZXF1aXJlZCcpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGlmIChwYXNzd29yZERhdGEubmV3UGFzc3dvcmQgIT09IHBhc3N3b3JkRGF0YS5jb25maXJtUGFzc3dvcmQpIHtcbiAgICAgIHRvYXN0RXJyb3IoJ05ldyBwYXNzd29yZHMgZG8gbm90IG1hdGNoJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgaWYgKHBhc3N3b3JkRGF0YS5uZXdQYXNzd29yZC5sZW5ndGggPCA4KSB7XG4gICAgICB0b2FzdEVycm9yKCdQYXNzd29yZCBtdXN0IGJlIGF0IGxlYXN0IDggY2hhcmFjdGVycyBsb25nJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgaWYgKHBhc3N3b3JkRGF0YS5uZXdQYXNzd29yZCA9PT0gcGFzc3dvcmREYXRhLmN1cnJlbnRQYXNzd29yZCkge1xuICAgICAgdG9hc3RFcnJvcignTmV3IHBhc3N3b3JkIG11c3QgYmUgZGlmZmVyZW50IGZyb20gY3VycmVudCBwYXNzd29yZCcpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHNldExvYWRpbmcodHJ1ZSk7XG5cbiAgICB0cnkge1xuICAgICAgLy8gRmlyc3QgdmVyaWZ5IGN1cnJlbnQgcGFzc3dvcmQgYnkgYXR0ZW1wdGluZyB0byBzaWduIGluXG4gICAgICBjb25zdCB7IGVycm9yOiBzaWduSW5FcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5zaWduSW5XaXRoUGFzc3dvcmQoe1xuICAgICAgICBlbWFpbDogdXNlcj8uZW1haWwgfHwgJycsXG4gICAgICAgIHBhc3N3b3JkOiBwYXNzd29yZERhdGEuY3VycmVudFBhc3N3b3JkXG4gICAgICB9KTtcblxuICAgICAgaWYgKHNpZ25JbkVycm9yKSB7XG4gICAgICAgIHRvYXN0LmVycm9yKCdDdXJyZW50IHBhc3N3b3JkIGlzIGluY29ycmVjdCcpO1xuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICAvLyBJZiBjdXJyZW50IHBhc3N3b3JkIGlzIGNvcnJlY3QsIHVwZGF0ZSB0byBuZXcgcGFzc3dvcmRcbiAgICAgIGNvbnN0IHsgZXJyb3I6IHVwZGF0ZUVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLnVwZGF0ZVVzZXIoe1xuICAgICAgICBwYXNzd29yZDogcGFzc3dvcmREYXRhLm5ld1Bhc3N3b3JkXG4gICAgICB9KTtcblxuICAgICAgaWYgKHVwZGF0ZUVycm9yKSB0aHJvdyB1cGRhdGVFcnJvcjtcblxuICAgICAgdG9hc3Quc3VjY2VzcygnUGFzc3dvcmQgdXBkYXRlZCBzdWNjZXNzZnVsbHknKTtcbiAgICAgIHNldFBhc3N3b3JkRGF0YSh7XG4gICAgICAgIGN1cnJlbnRQYXNzd29yZDogJycsXG4gICAgICAgIG5ld1Bhc3N3b3JkOiAnJyxcbiAgICAgICAgY29uZmlybVBhc3N3b3JkOiAnJ1xuICAgICAgfSk7XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcignUGFzc3dvcmQgdXBkYXRlIGVycm9yOicsIGVycm9yKTtcbiAgICAgIHRvYXN0LmVycm9yKGVycm9yLm1lc3NhZ2UgfHwgJ0ZhaWxlZCB0byB1cGRhdGUgcGFzc3dvcmQnKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZU5vdGlmaWNhdGlvblVwZGF0ZSA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRMb2FkaW5nKHRydWUpO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIFNhdmUgdG8gbG9jYWwgc3RvcmFnZSBhbmQgdXNlciBtZXRhZGF0YVxuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ25vdGlmaWNhdGlvbl9zZXR0aW5ncycsIEpTT04uc3RyaW5naWZ5KG5vdGlmaWNhdGlvblNldHRpbmdzKSk7XG5cbiAgICAgIC8vIEFsc28gc2F2ZSB0byB1c2VyIG1ldGFkYXRhIGZvciBwZXJzaXN0ZW5jZSBhY3Jvc3MgZGV2aWNlc1xuICAgICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC51cGRhdGVVc2VyKHtcbiAgICAgICAgZGF0YToge1xuICAgICAgICAgIG5vdGlmaWNhdGlvbl9wcmVmZXJlbmNlczogbm90aWZpY2F0aW9uU2V0dGluZ3NcbiAgICAgICAgfVxuICAgICAgfSk7XG5cbiAgICAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3I7XG5cbiAgICAgIHRvYXN0LnN1Y2Nlc3MoJ05vdGlmaWNhdGlvbiBwcmVmZXJlbmNlcyBzYXZlZCBzdWNjZXNzZnVsbHknKTtcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdOb3RpZmljYXRpb24gdXBkYXRlIGVycm9yOicsIGVycm9yKTtcbiAgICAgIHRvYXN0LmVycm9yKCdGYWlsZWQgdG8gc2F2ZSBub3RpZmljYXRpb24gcHJlZmVyZW5jZXMnKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVBhc3N3b3JkUmVzZXQgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCF1c2VyPy5lbWFpbCkge1xuICAgICAgdG9hc3QuZXJyb3IoJ05vIGVtYWlsIGFkZHJlc3MgZm91bmQnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBzZXRJc1Jlc2V0TG9hZGluZyh0cnVlKTtcbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5yZXNldFBhc3N3b3JkRm9yRW1haWwodXNlci5lbWFpbCwge1xuICAgICAgICByZWRpcmVjdFRvOiBgJHt3aW5kb3cubG9jYXRpb24ub3JpZ2lufS9hdXRoL3Jlc2V0LXBhc3N3b3JkYFxuICAgICAgfSk7XG5cbiAgICAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3I7XG5cbiAgICAgIHRvYXN0LnN1Y2Nlc3MoJ1Bhc3N3b3JkIHJlc2V0IGVtYWlsIHNlbnQhJywgJ0NoZWNrIHlvdXIgaW5ib3ggZm9yIGluc3RydWN0aW9ucyB0byByZXNldCB5b3VyIHBhc3N3b3JkLicpO1xuICAgICAgc2V0U2hvd1Jlc2V0TW9kYWwoZmFsc2UpO1xuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1Bhc3N3b3JkIHJlc2V0IGVycm9yOicsIGVycm9yKTtcbiAgICAgIHRvYXN0LmVycm9yKCdGYWlsZWQgdG8gc2VuZCByZXNldCBlbWFpbCcsIGVycm9yLm1lc3NhZ2UgfHwgJ1BsZWFzZSB0cnkgYWdhaW4uJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzUmVzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRW1haWxDaGFuZ2UgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFlbWFpbENoYW5nZURhdGEubmV3RW1haWwudHJpbSgpKSB7XG4gICAgICB0b2FzdC5lcnJvcignUGxlYXNlIGVudGVyIGEgbmV3IGVtYWlsIGFkZHJlc3MnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBpZiAoIWVtYWlsQ2hhbmdlRGF0YS5jdXJyZW50UGFzc3dvcmQudHJpbSgpKSB7XG4gICAgICB0b2FzdC5lcnJvcignQ3VycmVudCBwYXNzd29yZCBpcyByZXF1aXJlZCB0byBjaGFuZ2UgZW1haWwnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBpZiAoZW1haWxDaGFuZ2VEYXRhLm5ld0VtYWlsID09PSB1c2VyPy5lbWFpbCkge1xuICAgICAgdG9hc3QuZXJyb3IoJ05ldyBlbWFpbCBtdXN0IGJlIGRpZmZlcmVudCBmcm9tIGN1cnJlbnQgZW1haWwnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICAvLyBCYXNpYyBlbWFpbCB2YWxpZGF0aW9uXG4gICAgY29uc3QgZW1haWxSZWdleCA9IC9eW15cXHNAXStAW15cXHNAXStcXC5bXlxcc0BdKyQvO1xuICAgIGlmICghZW1haWxSZWdleC50ZXN0KGVtYWlsQ2hhbmdlRGF0YS5uZXdFbWFpbCkpIHtcbiAgICAgIHRvYXN0LmVycm9yKCdQbGVhc2UgZW50ZXIgYSB2YWxpZCBlbWFpbCBhZGRyZXNzJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgc2V0SXNFbWFpbExvYWRpbmcodHJ1ZSk7XG4gICAgdHJ5IHtcbiAgICAgIC8vIEZpcnN0IHZlcmlmeSBjdXJyZW50IHBhc3N3b3JkXG4gICAgICBjb25zdCB7IGVycm9yOiBzaWduSW5FcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5zaWduSW5XaXRoUGFzc3dvcmQoe1xuICAgICAgICBlbWFpbDogdXNlcj8uZW1haWwgfHwgJycsXG4gICAgICAgIHBhc3N3b3JkOiBlbWFpbENoYW5nZURhdGEuY3VycmVudFBhc3N3b3JkXG4gICAgICB9KTtcblxuICAgICAgaWYgKHNpZ25JbkVycm9yKSB7XG4gICAgICAgIHRvYXN0LmVycm9yKCdDdXJyZW50IHBhc3N3b3JkIGlzIGluY29ycmVjdCcpO1xuICAgICAgICBzZXRJc0VtYWlsTG9hZGluZyhmYWxzZSk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgLy8gVXBkYXRlIGVtYWlsXG4gICAgICBjb25zdCB7IGVycm9yOiB1cGRhdGVFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC51cGRhdGVVc2VyKHtcbiAgICAgICAgZW1haWw6IGVtYWlsQ2hhbmdlRGF0YS5uZXdFbWFpbFxuICAgICAgfSk7XG5cbiAgICAgIGlmICh1cGRhdGVFcnJvcikgdGhyb3cgdXBkYXRlRXJyb3I7XG5cbiAgICAgIHRvYXN0LnN1Y2Nlc3MoJ0VtYWlsIGNoYW5nZSBpbml0aWF0ZWQhJywgJ0NoZWNrIGJvdGggeW91ciBvbGQgYW5kIG5ldyBlbWFpbCBhZGRyZXNzZXMgZm9yIGNvbmZpcm1hdGlvbiBpbnN0cnVjdGlvbnMuJyk7XG4gICAgICBzZXRTaG93Q2hhbmdlRW1haWxNb2RhbChmYWxzZSk7XG4gICAgICBzZXRFbWFpbENoYW5nZURhdGEoeyBuZXdFbWFpbDogJycsIGN1cnJlbnRQYXNzd29yZDogJycgfSk7XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcignRW1haWwgY2hhbmdlIGVycm9yOicsIGVycm9yKTtcbiAgICAgIHRvYXN0LmVycm9yKCdGYWlsZWQgdG8gY2hhbmdlIGVtYWlsJywgZXJyb3IubWVzc2FnZSB8fCAnUGxlYXNlIHRyeSBhZ2Fpbi4nKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNFbWFpbExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVBY2NvdW50RGVsZXRpb24gPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCF1c2VyKSByZXR1cm47XG5cbiAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgIHRyeSB7XG4gICAgICAvLyBDYWxsIG91ciBhY2NvdW50IGRlbGV0aW9uIEFQSVxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS91c2VyL2RlbGV0ZS1hY2NvdW50Jywge1xuICAgICAgICBtZXRob2Q6ICdERUxFVEUnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJ1xuICAgICAgICB9XG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvckRhdGEuZXJyb3IgfHwgJ0ZhaWxlZCB0byBkZWxldGUgYWNjb3VudCcpO1xuICAgICAgfVxuXG4gICAgICB0b2FzdC5zdWNjZXNzKCdBY2NvdW50IGRlbGV0ZWQgc3VjY2Vzc2Z1bGx5LiBZb3Ugd2lsbCBiZSByZWRpcmVjdGVkIHRvIHRoZSBob21lcGFnZS4nKTtcblxuICAgICAgLy8gU2lnbiBvdXQgYW5kIHJlZGlyZWN0XG4gICAgICBhd2FpdCBzdXBhYmFzZS5hdXRoLnNpZ25PdXQoKTtcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvJztcbiAgICAgIH0sIDIwMDApO1xuXG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcignQWNjb3VudCBkZWxldGlvbiBlcnJvcjonLCBlcnJvcik7XG4gICAgICB0b2FzdC5lcnJvcihlcnJvci5tZXNzYWdlIHx8ICdGYWlsZWQgdG8gZGVsZXRlIGFjY291bnQuIFBsZWFzZSBjb250YWN0IHN1cHBvcnQuJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHNwYWNlLXktNlwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+U2V0dGluZ3M8L2gxPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbXQtMVwiPk1hbmFnZSB5b3VyIGFjY291bnQgc2V0dGluZ3MgYW5kIHByZWZlcmVuY2VzPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogTmF2aWdhdGlvbiBUYWJzICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItYiBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgPG5hdiBjbGFzc05hbWU9XCItbWItcHggZmxleCBzcGFjZS14LThcIj5cbiAgICAgICAgICB7c2lkZWJhckl0ZW1zLm1hcCgoaXRlbSkgPT4ge1xuICAgICAgICAgICAgY29uc3QgSWNvbiA9IGl0ZW0uaWNvbjtcbiAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBrZXk9e2l0ZW0uaWR9XG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlU2VjdGlvbihpdGVtLmlkKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBweS00IHB4LTEgYm9yZGVyLWItMiBmb250LW1lZGl1bSB0ZXh0LXNtIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICBhY3RpdmVTZWN0aW9uID09PSBpdGVtLmlkXG4gICAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1vcmFuZ2UtNTAwIHRleHQtb3JhbmdlLTYwMCdcbiAgICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLXRyYW5zcGFyZW50IHRleHQtZ3JheS01MDAgaG92ZXI6dGV4dC1ncmF5LTcwMCBob3Zlcjpib3JkZXItZ3JheS0zMDAnXG4gICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8SWNvbiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICB7aXRlbS5sYWJlbH1cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICApO1xuICAgICAgICAgIH0pfVxuICAgICAgICA8L25hdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogQ29udGVudCBBcmVhICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy00eGxcIj5cbiAgICAgICAgICAgIHthY3RpdmVTZWN0aW9uID09PSAnYWNjb3VudCcgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktOFwiPlxuICAgICAgICAgICAgICAgIHsvKiBFbWFpbCBBZGRyZXNzIFNlY3Rpb24gKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcC02XCI+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPkVtYWlsIGFkZHJlc3M8L2gzPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+WW91ciBlbWFpbCBhZGRyZXNzIGlzIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj57dXNlcj8uZW1haWx9PC9zcGFuPjwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDAgbXQtMVwiPlRoaXMgaXMgdXNlZCBmb3IgbG9naW4gYW5kIGltcG9ydGFudCBub3RpZmljYXRpb25zPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtYmx1ZS02MDAgaG92ZXI6dGV4dC1ibHVlLTcwMCBob3ZlcjpiZy1ibHVlLTUwIHJvdW5kZWQtbWQgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dDaGFuZ2VFbWFpbE1vZGFsKHRydWUpfVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgQ2hhbmdlXG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogQWNjb3VudCBJbmZvcm1hdGlvbiAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBwLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi02XCI+QWNjb3VudCBJbmZvcm1hdGlvbjwvaDM+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0xXCI+RnVsbCBOYW1lPC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3VzZXI/LnVzZXJfbWV0YWRhdGE/LmZ1bGxfbmFtZSB8fCB1c2VyPy51c2VyX21ldGFkYXRhPy5maXJzdF9uYW1lICsgJyAnICsgdXNlcj8udXNlcl9tZXRhZGF0YT8ubGFzdF9uYW1lIHx8ICdOb3Qgc2V0J31cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0xXCI+QWNjb3VudCBDcmVhdGVkPC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3VzZXI/LmNyZWF0ZWRfYXQgPyBuZXcgRGF0ZSh1c2VyLmNyZWF0ZWRfYXQpLnRvTG9jYWxlRGF0ZVN0cmluZygnZW4tVVMnLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgeWVhcjogJ251bWVyaWMnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1vbnRoOiAnbG9uZycsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF5OiAnbnVtZXJpYydcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfSkgOiAnVW5rbm93bid9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTFcIj5BY2NvdW50IFVzYWdlPC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0xIHRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cD57dXNlclN0YXRzLmNvbmZpZ0NvdW50fSBjb25maWd1cmF0aW9uczwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHA+e3VzZXJTdGF0cy5hcGlLZXlDb3VudH0gQVBJIGtleXM8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwPnt1c2VyU3RhdHMudXNlckFwaUtleUNvdW50fSB1c2VyLWdlbmVyYXRlZCBrZXlzPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogUGFzc3dvcmQgU2VjdGlvbiAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBwLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi02XCI+UGFzc3dvcmQ8L2gzPlxuICAgICAgICAgICAgICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVBhc3N3b3JkQ2hhbmdlfSBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImN1cnJlbnRQYXNzd29yZFwiIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPkN1cnJlbnQgcGFzc3dvcmQ8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgbXQtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiY3VycmVudFBhc3N3b3JkXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT17c2hvd0N1cnJlbnRQYXNzd29yZCA/IFwidGV4dFwiIDogXCJwYXNzd29yZFwifVxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17cGFzc3dvcmREYXRhLmN1cnJlbnRQYXNzd29yZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRQYXNzd29yZERhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBjdXJyZW50UGFzc3dvcmQ6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLigKLigKLigKLigKLigKLigKLigKLigKLigKLigKJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHItMTAgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1sZyB0ZXh0LWdyYXktOTAwIHBsYWNlaG9sZGVyLWdyYXktNTAwIGJnLXdoaXRlIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9yYW5nZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50IHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0N1cnJlbnRQYXNzd29yZCghc2hvd0N1cnJlbnRQYXNzd29yZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHJpZ2h0LTMgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS02MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7c2hvd0N1cnJlbnRQYXNzd29yZCA/IDxFeWVTbGFzaEljb24gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+IDogPEV5ZUljb24gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+fVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJuZXdQYXNzd29yZFwiIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPk5ldyBwYXNzd29yZDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIG10LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJuZXdQYXNzd29yZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT17c2hvd05ld1Bhc3N3b3JkID8gXCJ0ZXh0XCIgOiBcInBhc3N3b3JkXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3Bhc3N3b3JkRGF0YS5uZXdQYXNzd29yZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFBhc3N3b3JkRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIG5ld1Bhc3N3b3JkOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLigKLigKLigKLigKLigKLigKLigKLigKLigKLigKJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBwci0xMCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIHRleHQtZ3JheS05MDAgcGxhY2Vob2xkZXItZ3JheS01MDAgYmctd2hpdGUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctb3JhbmdlLTUwMCBmb2N1czpib3JkZXItdHJhbnNwYXJlbnQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd05ld1Bhc3N3b3JkKCFzaG93TmV3UGFzc3dvcmQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHJpZ2h0LTMgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS02MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge3Nob3dOZXdQYXNzd29yZCA/IDxFeWVTbGFzaEljb24gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+IDogPEV5ZUljb24gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+fVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImNvbmZpcm1QYXNzd29yZFwiIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPkNvbmZpcm0gbmV3IHBhc3N3b3JkPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgbXQtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cImNvbmZpcm1QYXNzd29yZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT17c2hvd0NvbmZpcm1QYXNzd29yZCA/IFwidGV4dFwiIDogXCJwYXNzd29yZFwifVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtwYXNzd29yZERhdGEuY29uZmlybVBhc3N3b3JkfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0UGFzc3dvcmREYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgY29uZmlybVBhc3N3b3JkOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLigKLigKLigKLigKLigKLigKLigKLigKLigKLigKJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBwci0xMCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIHRleHQtZ3JheS05MDAgcGxhY2Vob2xkZXItZ3JheS01MDAgYmctd2hpdGUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctb3JhbmdlLTUwMCBmb2N1czpib3JkZXItdHJhbnNwYXJlbnQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0NvbmZpcm1QYXNzd29yZCghc2hvd0NvbmZpcm1QYXNzd29yZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTYwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c2hvd0NvbmZpcm1QYXNzd29yZCA/IDxFeWVTbGFzaEljb24gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+IDogPEV5ZUljb24gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+fVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgQ2FuJ3QgcmVtZW1iZXIgeW91ciBjdXJyZW50IHBhc3N3b3JkPyA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwIGhvdmVyOnRleHQtYmx1ZS03MDAgZm9udC1tZWRpdW1cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93UmVzZXRNb2RhbCh0cnVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgUmVzZXQgeW91ciBwYXNzd29yZFxuICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiB0eXBlPVwic3VibWl0XCIgZGlzYWJsZWQ9e2xvYWRpbmd9IGNsYXNzTmFtZT1cImJnLW9yYW5nZS02MDAgaG92ZXI6Ymctb3JhbmdlLTcwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2xvYWRpbmcgPyAnU2F2aW5nLi4uJyA6ICdTYXZlIHBhc3N3b3JkJ31cbiAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Zvcm0+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAge2FjdGl2ZVNlY3Rpb24gPT09ICdub3RpZmljYXRpb25zJyAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS04XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcC02XCI+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItNlwiPk5vdGlmaWNhdGlvbiBQcmVmZXJlbmNlczwvaDM+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgICAgICAgICAgICB7T2JqZWN0LmVudHJpZXMobm90aWZpY2F0aW9uU2V0dGluZ3MpLm1hcCgoW2tleSwgdmFsdWVdKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2tleX0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHB5LTQgYm9yZGVyLWIgYm9yZGVyLWdyYXktMTAwIGxhc3Q6Ym9yZGVyLWItMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtrZXkgPT09ICdlbWFpbE5vdGlmaWNhdGlvbnMnICYmICdFbWFpbCBOb3RpZmljYXRpb25zJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7a2V5ID09PSAnc2VjdXJpdHlBbGVydHMnICYmICdTZWN1cml0eSBBbGVydHMnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtrZXkgPT09ICd1c2FnZUFsZXJ0cycgJiYgJ1VzYWdlIEFsZXJ0cyd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2tleSA9PT0gJ21hcmtldGluZ0VtYWlscycgJiYgJ01hcmtldGluZyBFbWFpbHMnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtrZXkgPT09ICdlbWFpbE5vdGlmaWNhdGlvbnMnICYmICdSZWNlaXZlIGdlbmVyYWwgZW1haWwgbm90aWZpY2F0aW9ucyd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2tleSA9PT0gJ3NlY3VyaXR5QWxlcnRzJyAmJiAnR2V0IG5vdGlmaWVkIGFib3V0IHNlY3VyaXR5IGV2ZW50cyd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2tleSA9PT0gJ3VzYWdlQWxlcnRzJyAmJiAnQWxlcnRzIGFib3V0IEFQSSB1c2FnZSBhbmQgbGltaXRzJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7a2V5ID09PSAnbWFya2V0aW5nRW1haWxzJyAmJiAnUHJvZHVjdCB1cGRhdGVzIGFuZCBwcm9tb3Rpb25hbCBjb250ZW50J31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwicmVsYXRpdmUgaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGN1cnNvci1wb2ludGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17dmFsdWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXROb3RpZmljYXRpb25TZXR0aW5ncyhwcmV2ID0+ICh7IC4uLnByZXYsIFtrZXldOiBlLnRhcmdldC5jaGVja2VkIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJzci1vbmx5IHBlZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTEgaC02IGJnLWdyYXktMjAwIHBlZXItZm9jdXM6b3V0bGluZS1ub25lIHBlZXItZm9jdXM6cmluZy00IHBlZXItZm9jdXM6cmluZy1vcmFuZ2UtMzAwIHJvdW5kZWQtZnVsbCBwZWVyIHBlZXItY2hlY2tlZDphZnRlcjp0cmFuc2xhdGUteC1mdWxsIHBlZXItY2hlY2tlZDphZnRlcjpib3JkZXItd2hpdGUgYWZ0ZXI6Y29udGVudC1bJyddIGFmdGVyOmFic29sdXRlIGFmdGVyOnRvcC1bMnB4XSBhZnRlcjpsZWZ0LVsycHhdIGFmdGVyOmJnLXdoaXRlIGFmdGVyOmJvcmRlci1ncmF5LTMwMCBhZnRlcjpib3JkZXIgYWZ0ZXI6cm91bmRlZC1mdWxsIGFmdGVyOmgtNSBhZnRlcjp3LTUgYWZ0ZXI6dHJhbnNpdGlvbi1hbGwgcGVlci1jaGVja2VkOmJnLW9yYW5nZS01MDBcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB0LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9e2hhbmRsZU5vdGlmaWNhdGlvblVwZGF0ZX0gZGlzYWJsZWQ9e2xvYWRpbmd9IGNsYXNzTmFtZT1cImJnLW9yYW5nZS02MDAgaG92ZXI6Ymctb3JhbmdlLTcwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2xvYWRpbmcgPyAnU2F2aW5nLi4uJyA6ICdTYXZlIHByZWZlcmVuY2VzJ31cbiAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7YWN0aXZlU2VjdGlvbiA9PT0gJ3NlY3VyaXR5JyAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS04XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcC02XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi02XCI+XG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPlNlY3VyaXR5IE92ZXJ2aWV3PC9oMz5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBweC0zIHB5LTEgYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwIHJvdW5kZWQtZnVsbCB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFNoaWVsZENoZWNrSWNvbiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICBTZWN1cmVcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC02XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItMlwiPlBhc3N3b3JkIFNlY3VyaXR5PC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBtYi0zXCI+Q2hhbmdlIHlvdXIgcGFzc3dvcmQgcmVndWxhcmx5IGZvciBiZXR0ZXIgc2VjdXJpdHk8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlU2VjdGlvbignYWNjb3VudCcpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LW9yYW5nZS02MDAgYm9yZGVyLW9yYW5nZS0zMDAgaG92ZXI6Ymctb3JhbmdlLTUwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgQ2hhbmdlIFBhc3N3b3JkXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItMVwiPkFjY291bnQgQWN0aXZpdHk8L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHt1c2VyU3RhdHMuY29uZmlnQ291bnQgKyB1c2VyU3RhdHMuYXBpS2V5Q291bnQgKyB1c2VyU3RhdHMudXNlckFwaUtleUNvdW50fSB0b3RhbCByZXNvdXJjZXMgY3JlYXRlZFxuICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTFcIj5BY2NvdW50IENyZWF0ZWQ8L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHt1c2VyPy5jcmVhdGVkX2F0ID8gbmV3IERhdGUodXNlci5jcmVhdGVkX2F0KS50b0xvY2FsZURhdGVTdHJpbmcoJ2VuLVVTJywge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHllYXI6ICdudW1lcmljJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtb250aDogJ2xvbmcnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRheTogJ251bWVyaWMnXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH0pIDogJ1Vua25vd24nfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHthY3RpdmVTZWN0aW9uID09PSAnZGFuZ2VyJyAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS04XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItcmVkLTIwMCBwLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTMgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgICA8VHJhc2hJY29uIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1yZWQtNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXJlZC05MDBcIj5EYW5nZXIgWm9uZTwvaDM+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtNTAgYm9yZGVyIGJvcmRlci1yZWQtMjAwIHJvdW5kZWQtbGcgcC02XCI+XG4gICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtcmVkLTkwMCBtYi0zXCI+RGVsZXRlIEFjY291bnQ8L2g0PlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcmVkLTcwMCBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgUGVybWFuZW50bHkgZGVsZXRlIHlvdXIgUm91S2V5IGFjY291bnQgYW5kIGFsbCBhc3NvY2lhdGVkIGRhdGEuIFRoaXMgYWN0aW9uIGNhbm5vdCBiZSB1bmRvbmUuXG4gICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBib3JkZXIgYm9yZGVyLXJlZC0yMDAgcm91bmRlZC1tZCBwLTQgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxoNSBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtcmVkLTkwMCBtYi0yXCI+VGhpcyB3aWxsIHBlcm1hbmVudGx5IGRlbGV0ZTo8L2g1PlxuICAgICAgICAgICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcmVkLTcwMCBzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxsaT7igKIge3VzZXJTdGF0cy5jb25maWdDb3VudH0gQVBJIGNvbmZpZ3VyYXRpb25zPC9saT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxsaT7igKIge3VzZXJTdGF0cy5hcGlLZXlDb3VudH0gQVBJIGtleXM8L2xpPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGxpPuKAoiB7dXNlclN0YXRzLnVzZXJBcGlLZXlDb3VudH0gdXNlci1nZW5lcmF0ZWQgQVBJIGtleXM8L2xpPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGxpPuKAoiBBbGwgdXNhZ2UgbG9ncyBhbmQgYW5hbHl0aWNzPC9saT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxsaT7igKIgWW91ciBzdWJzY3JpcHRpb24gYW5kIGJpbGxpbmcgaW5mb3JtYXRpb248L2xpPlxuICAgICAgICAgICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMCBib3JkZXItcmVkLTMwMCBob3ZlcjpiZy1yZWQtMTAwIGhvdmVyOmJvcmRlci1yZWQtNDAwXCJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93RGVsZXRlTW9kYWwodHJ1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmd9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICBEZWxldGUgbXkgYWNjb3VudFxuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIENoYW5nZSBFbWFpbCBNb2RhbCAqL31cbiAgICAgIHtzaG93Q2hhbmdlRW1haWxNb2RhbCAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ibGFjay81MCBiYWNrZHJvcC1ibHVyLXNtIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHAtNCB6LTUwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLXhsIHNoYWRvdy0yeGwgbWF4LXctbWQgdy1mdWxsIHAtNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMyBtYi00XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMCBoLTEwIGJnLW9yYW5nZS0xMDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPEVudmVsb3BlSWNvbiBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtb3JhbmdlLTYwMFwiIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5DaGFuZ2UgRW1haWwgQWRkcmVzczwvaDM+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtYi02XCI+XG4gICAgICAgICAgICAgIEVudGVyIHlvdXIgbmV3IGVtYWlsIGFkZHJlc3MgYW5kIGN1cnJlbnQgcGFzc3dvcmQgdG8gY2hhbmdlIHlvdXIgZW1haWwuXG4gICAgICAgICAgICAgIFlvdSdsbCBuZWVkIHRvIHZlcmlmeSBib3RoIHlvdXIgb2xkIGFuZCBuZXcgZW1haWwgYWRkcmVzc2VzLlxuICAgICAgICAgICAgPC9wPlxuXG4gICAgICAgICAgICA8Zm9ybSBvblN1Ym1pdD17KGUpID0+IHsgZS5wcmV2ZW50RGVmYXVsdCgpOyBoYW5kbGVFbWFpbENoYW5nZSgpOyB9fSBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cIm5ld0VtYWlsXCIgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgIE5ldyBFbWFpbCBBZGRyZXNzXG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIGlkPVwibmV3RW1haWxcIlxuICAgICAgICAgICAgICAgICAgdHlwZT1cImVtYWlsXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtlbWFpbENoYW5nZURhdGEubmV3RW1haWx9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEVtYWlsQ2hhbmdlRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIG5ld0VtYWlsOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHlvdXIgbmV3IGVtYWlsIGFkZHJlc3NcIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctb3JhbmdlLTUwMCBmb2N1czpib3JkZXItdHJhbnNwYXJlbnQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cImN1cnJlbnRQYXNzd29yZEVtYWlsXCIgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgIEN1cnJlbnQgUGFzc3dvcmRcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgaWQ9XCJjdXJyZW50UGFzc3dvcmRFbWFpbFwiXG4gICAgICAgICAgICAgICAgICB0eXBlPVwicGFzc3dvcmRcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2VtYWlsQ2hhbmdlRGF0YS5jdXJyZW50UGFzc3dvcmR9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEVtYWlsQ2hhbmdlRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIGN1cnJlbnRQYXNzd29yZDogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciB5b3VyIGN1cnJlbnQgcGFzc3dvcmRcIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctb3JhbmdlLTUwMCBmb2N1czpib3JkZXItdHJhbnNwYXJlbnQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1vcmFuZ2UtNTAgYm9yZGVyIGJvcmRlci1vcmFuZ2UtMjAwIHJvdW5kZWQtbGcgcC00IG10LTRcIj5cbiAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LW9yYW5nZS05MDAgbWItMlwiPldoYXQgaGFwcGVucyBuZXh0OjwvaDQ+XG4gICAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1vcmFuZ2UtNzAwIHNwYWNlLXktMVwiPlxuICAgICAgICAgICAgICAgICAgPGxpPuKAoiBZb3UnbGwgcmVjZWl2ZSBjb25maXJtYXRpb24gZW1haWxzIGF0IGJvdGggYWRkcmVzc2VzPC9saT5cbiAgICAgICAgICAgICAgICAgIDxsaT7igKIgQ2xpY2sgdGhlIGxpbmtzIGluIGJvdGggZW1haWxzIHRvIGNvbmZpcm0gdGhlIGNoYW5nZTwvbGk+XG4gICAgICAgICAgICAgICAgICA8bGk+4oCiIFlvdXIgZW1haWwgd2lsbCBiZSB1cGRhdGVkIG9uY2UgYm90aCBhcmUgdmVyaWZpZWQ8L2xpPlxuICAgICAgICAgICAgICAgICAgPGxpPuKAoiBZb3UgY2FuIGNvbnRpbnVlIHVzaW5nIHlvdXIgY3VycmVudCBlbWFpbCB1bnRpbCB0aGVuPC9saT5cbiAgICAgICAgICAgICAgICA8L3VsPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTMgcHQtNFwiPlxuICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBzZXRTaG93Q2hhbmdlRW1haWxNb2RhbChmYWxzZSk7XG4gICAgICAgICAgICAgICAgICAgIHNldEVtYWlsQ2hhbmdlRGF0YSh7IG5ld0VtYWlsOiAnJywgY3VycmVudFBhc3N3b3JkOiAnJyB9KTtcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTFcIlxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzRW1haWxMb2FkaW5nfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIENhbmNlbFxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzRW1haWxMb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIGJnLW9yYW5nZS02MDAgaG92ZXI6Ymctb3JhbmdlLTcwMCB0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7aXNFbWFpbExvYWRpbmcgPyAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNCBoLTQgYm9yZGVyLTIgYm9yZGVyLXdoaXRlIGJvcmRlci10LXRyYW5zcGFyZW50IHJvdW5kZWQtZnVsbCBhbmltYXRlLXNwaW4gbXItMlwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIENoYW5naW5nLi4uXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgJ0NoYW5nZSBFbWFpbCdcbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9mb3JtPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBQYXNzd29yZCBSZXNldCBDb25maXJtYXRpb24gTW9kYWwgKi99XG4gICAgICB7c2hvd1Jlc2V0TW9kYWwgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2svNTAgYmFja2Ryb3AtYmx1ci1zbSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBwLTQgei01MFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC14bCBzaGFkb3ctMnhsIG1heC13LW1kIHctZnVsbCBwLTZcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTMgbWItNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgaC0xMCBiZy1ibHVlLTEwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8RW52ZWxvcGVJY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1ibHVlLTYwMFwiIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5SZXNldCBZb3VyIFBhc3N3b3JkPC9oMz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG1iLTRcIj5cbiAgICAgICAgICAgICAgV2UnbGwgc2VuZCBhIHBhc3N3b3JkIHJlc2V0IGxpbmsgdG8gPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57dXNlcj8uZW1haWx9PC9zcGFuPi5cbiAgICAgICAgICAgICAgWW91IGNhbiB1c2UgdGhpcyBsaW5rIHRvIGNyZWF0ZSBhIG5ldyBwYXNzd29yZC5cbiAgICAgICAgICAgIDwvcD5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ibHVlLTUwIGJvcmRlciBib3JkZXItYmx1ZS0yMDAgcm91bmRlZC1sZyBwLTQgbWItNlwiPlxuICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWJsdWUtOTAwIG1iLTJcIj5XaGF0IGhhcHBlbnMgbmV4dDo8L2g0PlxuICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtNzAwIHNwYWNlLXktMVwiPlxuICAgICAgICAgICAgICAgIDxsaT7igKIgWW91J2xsIHJlY2VpdmUgYW4gZW1haWwgd2l0aCBhIHNlY3VyZSByZXNldCBsaW5rPC9saT5cbiAgICAgICAgICAgICAgICA8bGk+4oCiIENsaWNrIHRoZSBsaW5rIHRvIGNyZWF0ZSBhIG5ldyBwYXNzd29yZDwvbGk+XG4gICAgICAgICAgICAgICAgPGxpPuKAoiBZb3VyIGN1cnJlbnQgcGFzc3dvcmQgd2lsbCByZW1haW4gYWN0aXZlIHVudGlsIHlvdSBjb21wbGV0ZSB0aGUgcmVzZXQ8L2xpPlxuICAgICAgICAgICAgICA8L3VsPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtM1wiPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dSZXNldE1vZGFsKGZhbHNlKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTFcIlxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc1Jlc2V0TG9hZGluZ31cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIENhbmNlbFxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVBhc3N3b3JkUmVzZXR9XG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzUmVzZXRMb2FkaW5nfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBiZy1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTcwMCB0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtpc1Jlc2V0TG9hZGluZyA/IChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTQgaC00IGJvcmRlci0yIGJvcmRlci13aGl0ZSBib3JkZXItdC10cmFuc3BhcmVudCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1zcGluIG1yLTJcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgU2VuZGluZy4uLlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICdTZW5kIFJlc2V0IExpbmsnXG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogRGVsZXRlIEFjY291bnQgQ29uZmlybWF0aW9uIE1vZGFsICovfVxuICAgICAge3Nob3dEZWxldGVNb2RhbCAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ibGFjay81MCBiYWNrZHJvcC1ibHVyLXNtIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHAtNCB6LTUwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLXhsIHNoYWRvdy0yeGwgbWF4LXctbWQgdy1mdWxsIHAtNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMyBtYi00XCI+XG4gICAgICAgICAgICAgIDxUcmFzaEljb24gY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LXJlZC01MDBcIiAvPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5EZWxldGUgQWNjb3VudDwvaDM+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtYi00XCI+XG4gICAgICAgICAgICAgIEFyZSB5b3UgYWJzb2x1dGVseSBzdXJlIHlvdSB3YW50IHRvIGRlbGV0ZSB5b3VyIGFjY291bnQ/IFRoaXMgYWN0aW9uIGNhbm5vdCBiZSB1bmRvbmUuXG4gICAgICAgICAgICA8L3A+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctcmVkLTUwIGJvcmRlciBib3JkZXItcmVkLTIwMCByb3VuZGVkLWxnIHAtNCBtYi02XCI+XG4gICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtcmVkLTkwMCBtYi0yXCI+VGhpcyB3aWxsIHBlcm1hbmVudGx5IGRlbGV0ZTo8L2g0PlxuICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXJlZC03MDAgc3BhY2UteS0xXCI+XG4gICAgICAgICAgICAgICAgPGxpPuKAoiB7dXNlclN0YXRzLmNvbmZpZ0NvdW50fSBBUEkgY29uZmlndXJhdGlvbnM8L2xpPlxuICAgICAgICAgICAgICAgIDxsaT7igKIge3VzZXJTdGF0cy5hcGlLZXlDb3VudH0gQVBJIGtleXM8L2xpPlxuICAgICAgICAgICAgICAgIDxsaT7igKIge3VzZXJTdGF0cy51c2VyQXBpS2V5Q291bnR9IHVzZXItZ2VuZXJhdGVkIEFQSSBrZXlzPC9saT5cbiAgICAgICAgICAgICAgICA8bGk+4oCiIEFsbCB1c2FnZSBsb2dzIGFuZCBhbmFseXRpY3M8L2xpPlxuICAgICAgICAgICAgICAgIDxsaT7igKIgWW91ciBzdWJzY3JpcHRpb24gYW5kIGJpbGxpbmcgaW5mb3JtYXRpb248L2xpPlxuICAgICAgICAgICAgICA8L3VsPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtM1wiPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dEZWxldGVNb2RhbChmYWxzZSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xXCJcbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZ31cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIENhbmNlbFxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUFjY291bnREZWxldGlvbn1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZ31cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgYmctcmVkLTYwMCBob3ZlcjpiZy1yZWQtNzAwIHRleHQtd2hpdGVcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2xvYWRpbmcgPyAnRGVsZXRpbmcuLi4nIDogJ0RlbGV0ZSBBY2NvdW50J31cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUm91dGVyIiwiVXNlckljb24iLCJTaGllbGRDaGVja0ljb24iLCJCZWxsSWNvbiIsIkV5ZUljb24iLCJFeWVTbGFzaEljb24iLCJUcmFzaEljb24iLCJFbnZlbG9wZUljb24iLCJCdXR0b24iLCJMYWJlbCIsInVzZVRvYXN0IiwiY3JlYXRlU3VwYWJhc2VCcm93c2VyQ2xpZW50IiwidXNlU3Vic2NyaXB0aW9uIiwiU2V0dGluZ3NQYWdlIiwidXNlciIsInJvdXRlciIsInN1cGFiYXNlIiwic3VjY2VzcyIsImVycm9yIiwidG9hc3RFcnJvciIsImFjdGl2ZVNlY3Rpb24iLCJzZXRBY3RpdmVTZWN0aW9uIiwibG9hZGluZyIsInNldExvYWRpbmciLCJzaG93Q3VycmVudFBhc3N3b3JkIiwic2V0U2hvd0N1cnJlbnRQYXNzd29yZCIsInNob3dOZXdQYXNzd29yZCIsInNldFNob3dOZXdQYXNzd29yZCIsInNob3dDb25maXJtUGFzc3dvcmQiLCJzZXRTaG93Q29uZmlybVBhc3N3b3JkIiwic2hvd0RlbGV0ZU1vZGFsIiwic2V0U2hvd0RlbGV0ZU1vZGFsIiwic2hvd1Jlc2V0TW9kYWwiLCJzZXRTaG93UmVzZXRNb2RhbCIsImlzUmVzZXRMb2FkaW5nIiwic2V0SXNSZXNldExvYWRpbmciLCJzaG93Q2hhbmdlRW1haWxNb2RhbCIsInNldFNob3dDaGFuZ2VFbWFpbE1vZGFsIiwiaXNFbWFpbExvYWRpbmciLCJzZXRJc0VtYWlsTG9hZGluZyIsImVtYWlsQ2hhbmdlRGF0YSIsInNldEVtYWlsQ2hhbmdlRGF0YSIsIm5ld0VtYWlsIiwiY3VycmVudFBhc3N3b3JkIiwicGFzc3dvcmREYXRhIiwic2V0UGFzc3dvcmREYXRhIiwibmV3UGFzc3dvcmQiLCJjb25maXJtUGFzc3dvcmQiLCJub3RpZmljYXRpb25TZXR0aW5ncyIsInNldE5vdGlmaWNhdGlvblNldHRpbmdzIiwiZW1haWxOb3RpZmljYXRpb25zIiwic2VjdXJpdHlBbGVydHMiLCJ1c2FnZUFsZXJ0cyIsIm1hcmtldGluZ0VtYWlscyIsInVzZXJTdGF0cyIsInNldFVzZXJTdGF0cyIsImNvbmZpZ0NvdW50IiwiYXBpS2V5Q291bnQiLCJ1c2VyQXBpS2V5Q291bnQiLCJzaWRlYmFySXRlbXMiLCJpZCIsImxhYmVsIiwiaWNvbiIsImxvYWRVc2VyU3RhdHMiLCJsb2FkTm90aWZpY2F0aW9uU2V0dGluZ3MiLCJjb25maWdSZXNwb25zZSIsImZldGNoIiwiY29uZmlnRGF0YSIsIm9rIiwianNvbiIsImNvbmZpZ3MiLCJBcnJheSIsImlzQXJyYXkiLCJ1c2VyS2V5c1Jlc3BvbnNlIiwidXNlcktleXNEYXRhIiwiYXBpX2tleXMiLCJ1c2VyS2V5cyIsInRvdGFsQXBpS2V5cyIsImNvbmZpZyIsImtleXNSZXNwb25zZSIsImtleXNEYXRhIiwibGVuZ3RoIiwiY29uc29sZSIsInNhdmVkU2V0dGluZ3MiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwiSlNPTiIsInBhcnNlIiwiaGFuZGxlUGFzc3dvcmRDaGFuZ2UiLCJlIiwicHJldmVudERlZmF1bHQiLCJ0cmltIiwic2lnbkluRXJyb3IiLCJhdXRoIiwic2lnbkluV2l0aFBhc3N3b3JkIiwiZW1haWwiLCJwYXNzd29yZCIsInRvYXN0IiwidXBkYXRlRXJyb3IiLCJ1cGRhdGVVc2VyIiwibWVzc2FnZSIsImhhbmRsZU5vdGlmaWNhdGlvblVwZGF0ZSIsInNldEl0ZW0iLCJzdHJpbmdpZnkiLCJkYXRhIiwibm90aWZpY2F0aW9uX3ByZWZlcmVuY2VzIiwiaGFuZGxlUGFzc3dvcmRSZXNldCIsInJlc2V0UGFzc3dvcmRGb3JFbWFpbCIsInJlZGlyZWN0VG8iLCJ3aW5kb3ciLCJsb2NhdGlvbiIsIm9yaWdpbiIsImhhbmRsZUVtYWlsQ2hhbmdlIiwiZW1haWxSZWdleCIsInRlc3QiLCJoYW5kbGVBY2NvdW50RGVsZXRpb24iLCJyZXNwb25zZSIsIm1ldGhvZCIsImhlYWRlcnMiLCJlcnJvckRhdGEiLCJFcnJvciIsInNpZ25PdXQiLCJzZXRUaW1lb3V0IiwiaHJlZiIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwicCIsIm5hdiIsIm1hcCIsIml0ZW0iLCJJY29uIiwiYnV0dG9uIiwib25DbGljayIsImgzIiwic3BhbiIsImg0IiwidXNlcl9tZXRhZGF0YSIsImZ1bGxfbmFtZSIsImZpcnN0X25hbWUiLCJsYXN0X25hbWUiLCJjcmVhdGVkX2F0IiwiRGF0ZSIsInRvTG9jYWxlRGF0ZVN0cmluZyIsInllYXIiLCJtb250aCIsImRheSIsImZvcm0iLCJvblN1Ym1pdCIsImh0bWxGb3IiLCJpbnB1dCIsInR5cGUiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwicHJldiIsInRhcmdldCIsInBsYWNlaG9sZGVyIiwicmVxdWlyZWQiLCJkaXNhYmxlZCIsIk9iamVjdCIsImVudHJpZXMiLCJrZXkiLCJjaGVja2VkIiwidmFyaWFudCIsInNpemUiLCJoNSIsInVsIiwibGkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/settings/page.tsx\n"));

/***/ })

});